"""
Windows环境启动脚本
Windows Environment Startup Script

专门为Windows环境优化的股票分析应用启动脚本
"""

import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def check_conda_environment():
    """检查conda环境"""
    try:
        conda_env = os.environ.get('CONDA_DEFAULT_ENV')
        if conda_env:
            print(f"✅ 当前conda环境: {conda_env}")
            if conda_env == 'stock':
                print("✅ 正在使用推荐的'stock'环境")
            else:
                print("⚠️ 建议使用'stock'环境")
            return True
        else:
            print("⚠️ 未检测到conda环境")
            return False
    except Exception as e:
        print(f"❌ 检查conda环境失败: {e}")
        return False

def check_dependencies():
    """检查依赖是否安装"""
    print("🔍 检查Python依赖包...")
    
    required_packages = [
        'streamlit',
        'pandas', 
        'numpy',
        'mysql.connector',
        'plotly',
        'bcrypt'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'mysql.connector':
                import mysql.connector
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def check_database_connection():
    """检查数据库连接"""
    print("\n🔍 检查数据库连接...")
    
    try:
        import mysql.connector
        from mysql.connector import Error
        
        # 尝试连接数据库
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='123456',
            database='stock_analysis'
        )
        
        if connection.is_connected():
            print("✅ 数据库连接成功")
            
            # 检查表是否存在
            cursor = connection.cursor()
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            if len(tables) >= 5:
                print(f"✅ 数据库表检查通过 ({len(tables)}个表)")
            else:
                print(f"⚠️ 数据库表数量不足 ({len(tables)}个表)")
                print("建议运行: python scripts/init_db.py")
            
            cursor.close()
            connection.close()
            return True
        else:
            print("❌ 数据库连接失败")
            return False
            
    except Error as e:
        print(f"❌ 数据库连接失败: {e}")
        print("\n💡 解决建议:")
        print("1. 确保MySQL服务已启动")
        print("2. 确认用户名密码: root/123456")
        print("3. 确认数据库名: stock_analysis")
        return False
    except ImportError:
        print("❌ 缺少mysql-connector-python依赖包")
        return False

def start_streamlit_app():
    """启动Streamlit应用"""
    print("\n🚀 启动Streamlit应用...")
    
    # 设置环境变量
    project_root = Path(__file__).parent.parent
    os.environ['PYTHONPATH'] = str(project_root)
    
    # 构建启动命令
    cmd = [
        sys.executable, "-m", "streamlit", "run",
        str(project_root / "app" / "main.py"),
        "--server.port=8501",
        "--server.address=localhost",
        "--server.headless=false",
        "--server.fileWatcherType=poll",
        "--browser.gatherUsageStats=false"
    ]
    
    try:
        print("📊 正在启动应用...")
        print("🌐 应用地址: http://localhost:8501")
        print("👤 演示账户: admin / admin123")
        print("⏹️ 按 Ctrl+C 停止应用")
        print("-" * 50)
        
        # 启动应用
        process = subprocess.Popen(
            cmd, 
            cwd=project_root,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # 等待应用启动
        startup_timeout = 30  # 30秒超时
        startup_time = 0
        app_started = False
        
        while startup_time < startup_timeout:
            if process.poll() is not None:
                # 进程已结束
                output = process.stdout.read()
                print(f"❌ 应用启动失败:\n{output}")
                return False
            
            time.sleep(1)
            startup_time += 1
            
            # 检查应用是否启动成功
            if startup_time == 5:  # 5秒后尝试打开浏览器
                try:
                    print("🌐 正在打开浏览器...")
                    webbrowser.open('http://localhost:8501')
                    app_started = True
                except Exception as e:
                    print(f"⚠️ 自动打开浏览器失败: {e}")
                    print("请手动访问: http://localhost:8501")
        
        if not app_started:
            print("🌐 请手动访问: http://localhost:8501")
        
        # 实时显示输出
        try:
            for line in process.stdout:
                print(line.strip())
        except KeyboardInterrupt:
            print("\n👋 正在停止应用...")
            process.terminate()
            process.wait()
            print("✅ 应用已停止")
        
        return True
        
    except FileNotFoundError:
        print("❌ 找不到streamlit命令")
        print("请确保已安装streamlit: pip install streamlit")
        return False
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def show_usage_tips():
    """显示使用提示"""
    print("\n💡 使用提示:")
    print("=" * 50)
    print("1. 应用启动后，浏览器会自动打开")
    print("2. 如果浏览器未自动打开，请手动访问: http://localhost:8501")
    print("3. 使用演示账户登录:")
    print("   - 用户名: admin")
    print("   - 密码: admin123")
    print("4. 应用包含三个主要功能:")
    print("   - 仪表板: 查看市场概览")
    print("   - 股票分析: 分析单只股票")
    print("   - 投资组合分析: 管理投资组合")
    print("5. 按 Ctrl+C 可以停止应用")

def main():
    """主函数"""
    print("🚀 股票分析系统 - Windows启动器")
    print("=" * 60)
    
    # 检查conda环境
    check_conda_environment()
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请先安装依赖包")
        input("按回车键退出...")
        return False
    
    # 检查数据库连接
    if not check_database_connection():
        print("\n⚠️ 数据库连接失败，但应用仍可启动（部分功能可能不可用）")
        choice = input("是否继续启动应用？(y/n): ").lower()
        if choice != 'y':
            return False
    
    # 显示使用提示
    show_usage_tips()
    
    # 启动应用
    input("\n按回车键启动应用...")
    return start_streamlit_app()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 启动已取消")
    except Exception as e:
        print(f"\n❌ 启动器错误: {e}")
        input("按回车键退出...")
