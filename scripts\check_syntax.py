"""
Python语法检查脚本
Python Syntax Check Script

检查项目中所有Python文件的语法错误
"""

import os
import ast
import sys
from pathlib import Path

def check_python_syntax(file_path):
    """检查单个Python文件的语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试解析AST
        ast.parse(content)
        return True, None
        
    except SyntaxError as e:
        return False, f"语法错误: {e.msg} (行 {e.lineno})"
    except Exception as e:
        return False, f"其他错误: {e}"

def find_python_files(directory):
    """查找目录中的所有Python文件"""
    python_files = []
    for root, dirs, files in os.walk(directory):
        # 跳过虚拟环境和缓存目录
        dirs[:] = [d for d in dirs if d not in ['.git', '__pycache__', '.pytest_cache', 'venv', 'env']]
        
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    return python_files

def main():
    """主函数"""
    print("🔍 Python语法检查工具")
    print("=" * 50)
    
    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    
    # 查找所有Python文件
    python_files = find_python_files(project_root)
    
    print(f"📁 找到 {len(python_files)} 个Python文件")
    print("-" * 50)
    
    errors = []
    success_count = 0
    
    for file_path in python_files:
        # 获取相对路径
        rel_path = os.path.relpath(file_path, project_root)
        
        # 检查语法
        is_valid, error_msg = check_python_syntax(file_path)
        
        if is_valid:
            print(f"✅ {rel_path}")
            success_count += 1
        else:
            print(f"❌ {rel_path}")
            print(f"   {error_msg}")
            errors.append((rel_path, error_msg))
    
    print("-" * 50)
    print(f"📊 检查结果:")
    print(f"   ✅ 成功: {success_count} 个文件")
    print(f"   ❌ 错误: {len(errors)} 个文件")
    
    if errors:
        print("\n🔧 需要修复的文件:")
        for file_path, error_msg in errors:
            print(f"   📄 {file_path}")
            print(f"      {error_msg}")
        
        print("\n💡 修复建议:")
        print("1. 检查字符串中的引号是否正确转义")
        print("2. 确保所有括号、方括号、花括号都正确配对")
        print("3. 检查缩进是否一致")
        print("4. 确保所有语句都以正确的语法结束")
        
        return False
    else:
        print("\n🎉 所有Python文件语法检查通过！")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
