"""
数据模型测试
Data Models Tests

测试用户和股票数据模型的功能
"""

import unittest
from unittest.mock import Mock, patch
import sys
import os
from datetime import date, datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.models.user import User, UserManager
from app.models.stock import Stock, StockManager

class TestUserModel(unittest.TestCase):
    """用户模型测试类"""
    
    def test_user_creation(self):
        """测试用户对象创建"""
        user = User(
            id=1,
            username="testuser",
            email="<EMAIL>",
            is_active=True
        )
        
        self.assertEqual(user.id, 1)
        self.assertEqual(user.username, "testuser")
        self.assertEqual(user.email, "<EMAIL>")
        self.assertTrue(user.is_active)
    
    @patch('app.models.user.get_db_connection')
    def test_user_manager_authenticate(self, mock_db):
        """测试用户认证功能"""
        # 模拟数据库连接
        mock_db_instance = Mock()
        mock_db.return_value = mock_db_instance
        
        # 模拟查询结果
        mock_db_instance.execute_query.return_value = [{
            'id': 1,
            'username': 'testuser',
            'password_hash': '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS',
            'email': '<EMAIL>',
            'created_at': datetime.now(),
            'updated_at': datetime.now(),
            'is_active': True
        }]
        
        user_manager = UserManager()
        
        # 测试认证（注意：这里的密码哈希对应 "admin123"）
        user = user_manager.authenticate_user("testuser", "admin123")
        
        self.assertIsNotNone(user)
        self.assertEqual(user.username, "testuser")

class TestStockModel(unittest.TestCase):
    """股票模型测试类"""
    
    def test_stock_creation(self):
        """测试股票对象创建"""
        stock = Stock(
            id=1,
            stock_code="000001",
            stock_name="平安银行",
            market="SZ",
            industry="银行",
            sector="金融"
        )
        
        self.assertEqual(stock.id, 1)
        self.assertEqual(stock.stock_code, "000001")
        self.assertEqual(stock.stock_name, "平安银行")
        self.assertEqual(stock.market, "SZ")
        self.assertEqual(stock.industry, "银行")
    
    @patch('app.models.stock.get_db_connection')
    def test_stock_manager_get_all_stocks(self, mock_db):
        """测试获取所有股票功能"""
        # 模拟数据库连接
        mock_db_instance = Mock()
        mock_db.return_value = mock_db_instance
        
        # 模拟查询结果
        mock_db_instance.execute_query.return_value = [
            {
                'id': 1,
                'stock_code': '000001',
                'stock_name': '平安银行',
                'market': 'SZ',
                'industry': '银行',
                'sector': '金融',
                'created_at': datetime.now(),
                'updated_at': datetime.now(),
                'is_active': True
            },
            {
                'id': 2,
                'stock_code': '000002',
                'stock_name': '万科A',
                'market': 'SZ',
                'industry': '房地产',
                'sector': '房地产',
                'created_at': datetime.now(),
                'updated_at': datetime.now(),
                'is_active': True
            }
        ]
        
        stock_manager = StockManager()
        stocks = stock_manager.get_all_stocks()
        
        self.assertEqual(len(stocks), 2)
        self.assertEqual(stocks[0].stock_code, "000001")
        self.assertEqual(stocks[1].stock_code, "000002")
    
    @patch('app.models.stock.get_db_connection')
    def test_stock_manager_get_latest_price(self, mock_db):
        """测试获取最新价格功能"""
        # 模拟数据库连接
        mock_db_instance = Mock()
        mock_db.return_value = mock_db_instance
        
        # 模拟查询结果
        mock_db_instance.execute_query.return_value = [
            {'close_price': 12.50}
        ]
        
        stock_manager = StockManager()
        price = stock_manager.get_latest_price("000001")
        
        self.assertEqual(price, 12.50)

class TestDatabaseConnection(unittest.TestCase):
    """数据库连接测试类"""
    
    @patch('app.database.connection.mysql.connector.connect')
    def test_database_connection(self, mock_connect):
        """测试数据库连接"""
        from app.database.connection import DatabaseManager
        
        # 模拟数据库连接
        mock_connection = Mock()
        mock_connection.is_connected.return_value = True
        mock_connect.return_value = mock_connection
        
        db_manager = DatabaseManager()
        result = db_manager.connect()
        
        self.assertTrue(result)
        mock_connect.assert_called_once()

if __name__ == '__main__':
    unittest.main()
