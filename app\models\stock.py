"""
股票数据模型
Stock Data Model

定义股票相关的数据结构和操作方法
"""

from dataclasses import dataclass
from typing import Optional, List, Dict, Any
from datetime import datetime, date
import pandas as pd
import numpy as np
from app.database.connection import get_db_connection

@dataclass
class Stock:
    """股票基础信息模型"""
    id: Optional[int] = None
    stock_code: str = ""
    stock_name: str = ""
    market: str = ""
    industry: Optional[str] = None
    sector: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_active: bool = True

@dataclass
class StockData:
    """股票历史数据模型"""
    id: Optional[int] = None
    stock_code: str = ""
    trade_date: date = None
    open_price: float = 0.0
    high_price: float = 0.0
    low_price: float = 0.0
    close_price: float = 0.0
    volume: int = 0
    amount: float = 0.0
    turnover_rate: float = 0.0
    created_at: Optional[datetime] = None

class StockManager:
    """股票数据管理器类"""
    
    def __init__(self):
        self.db = get_db_connection()
    
    def get_all_stocks(self) -> List[Stock]:
        """获取所有股票列表"""
        try:
            query = "SELECT * FROM stocks WHERE is_active = TRUE ORDER BY stock_code"
            result = self.db.execute_query(query)
            
            stocks = []
            if result:
                for row in result:
                    stock = Stock(
                        id=row['id'],
                        stock_code=row['stock_code'],
                        stock_name=row['stock_name'],
                        market=row['market'],
                        industry=row['industry'],
                        sector=row['sector'],
                        created_at=row['created_at'],
                        updated_at=row['updated_at'],
                        is_active=row['is_active']
                    )
                    stocks.append(stock)
            return stocks
        except Exception as e:
            print(f"获取股票列表失败: {e}")
            return []
    
    def get_stock_by_code(self, stock_code: str) -> Optional[Stock]:
        """根据股票代码获取股票信息"""
        try:
            query = "SELECT * FROM stocks WHERE stock_code = %s AND is_active = TRUE"
            result = self.db.execute_query(query, (stock_code,))
            
            if result and len(result) > 0:
                row = result[0]
                return Stock(
                    id=row['id'],
                    stock_code=row['stock_code'],
                    stock_name=row['stock_name'],
                    market=row['market'],
                    industry=row['industry'],
                    sector=row['sector'],
                    created_at=row['created_at'],
                    updated_at=row['updated_at'],
                    is_active=row['is_active']
                )
            return None
        except Exception as e:
            print(f"获取股票信息失败: {e}")
            return None
    
    def get_stock_data(self, stock_code: str, start_date: Optional[date] = None, 
                      end_date: Optional[date] = None) -> pd.DataFrame:
        """获取股票历史数据"""
        try:
            query = """
                SELECT * FROM stock_data 
                WHERE stock_code = %s
            """
            params = [stock_code]
            
            if start_date:
                query += " AND trade_date >= %s"
                params.append(start_date)
            
            if end_date:
                query += " AND trade_date <= %s"
                params.append(end_date)
            
            query += " ORDER BY trade_date"
            
            result = self.db.execute_query(query, tuple(params))
            
            if result:
                df = pd.DataFrame(result)
                df['trade_date'] = pd.to_datetime(df['trade_date'])
                df.set_index('trade_date', inplace=True)
                return df
            else:
                return pd.DataFrame()
        except Exception as e:
            print(f"获取股票数据失败: {e}")
            return pd.DataFrame()
    
    def get_latest_price(self, stock_code: str) -> Optional[float]:
        """获取股票最新价格"""
        try:
            query = """
                SELECT close_price FROM stock_data 
                WHERE stock_code = %s 
                ORDER BY trade_date DESC 
                LIMIT 1
            """
            result = self.db.execute_query(query, (stock_code,))
            
            if result and len(result) > 0:
                return float(result[0]['close_price'])
            return None
        except Exception as e:
            print(f"获取最新价格失败: {e}")
            return None
    
    def calculate_returns(self, stock_code: str, period_days: int = 30) -> Dict[str, float]:
        """计算股票收益率指标"""
        try:
            df = self.get_stock_data(stock_code)
            if df.empty:
                return {}
            
            # 计算日收益率
            df['daily_return'] = df['close_price'].pct_change()
            
            # 获取指定期间的数据
            recent_data = df.tail(period_days)
            
            # 计算各种指标
            total_return = (recent_data['close_price'].iloc[-1] / recent_data['close_price'].iloc[0] - 1) * 100
            daily_returns = recent_data['daily_return'].dropna()
            
            # 年化收益率
            annual_return = (1 + total_return/100) ** (252/period_days) - 1
            
            # 波动率（年化）
            volatility = daily_returns.std() * np.sqrt(252) * 100
            
            # 夏普比率（假设无风险利率为3%）
            risk_free_rate = 0.03
            sharpe_ratio = (annual_return - risk_free_rate) / (volatility/100) if volatility > 0 else 0
            
            # 最大回撤
            cumulative = (1 + daily_returns).cumprod()
            rolling_max = cumulative.expanding().max()
            drawdown = (cumulative - rolling_max) / rolling_max
            max_drawdown = drawdown.min() * 100
            
            return {
                'total_return': round(total_return, 2),
                'annual_return': round(annual_return * 100, 2),
                'volatility': round(volatility, 2),
                'sharpe_ratio': round(sharpe_ratio, 2),
                'max_drawdown': round(max_drawdown, 2)
            }
        except Exception as e:
            print(f"计算收益率指标失败: {e}")
            return {}
