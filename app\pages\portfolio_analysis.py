"""
投资组合分析页面
Portfolio Analysis Page

提供投资组合创建、管理和分析功能
"""

import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from app.models.stock import StockManager
from app.utils.helpers import (
    format_number, format_percentage, format_currency,
    display_metric_cards, create_portfolio_pie_chart, create_line_chart,
    create_returns_comparison_chart
)

def portfolio_analysis_page():
    """投资组合分析主页面"""

    # 页面标题
    st.markdown("""
    <div style="text-align: center; padding: 2rem 0 1rem 0;">
        <h1>💼 投资组合分析</h1>
        <p style="color: #666; font-size: 1.1rem;">构建专业投资组合，优化资产配置策略</p>
    </div>
    """, unsafe_allow_html=True)

    # 初始化股票管理器
    stock_manager = StockManager()

    # 获取所有股票列表
    stocks = stock_manager.get_all_stocks()

    if not stocks:
        st.error("⚠️ 暂无股票数据，请联系管理员检查数据库连接")
        return

    # 创建选项卡 - 使用更好的样式
    st.markdown("### 📋 功能导航")
    tab1, tab2, tab3 = st.tabs([
        "🎯 创建投资组合",
        "📊 组合风险分析",
        "📈 历史表现回测"
    ])
    
    with tab1:
        create_portfolio_tab(stock_manager, stocks)
    
    with tab2:
        analyze_portfolio_tab(stock_manager, stocks)
    
    with tab3:
        portfolio_performance_tab(stock_manager)

def create_portfolio_tab(stock_manager: StockManager, stocks):
    """创建投资组合选项卡"""
    
    st.subheader("🎯 创建投资组合")
    
    # 组合基本信息
    col1, col2 = st.columns(2)
    
    with col1:
        portfolio_name = st.text_input("📝 组合名称", placeholder="例如：稳健型组合")

    with col2:
        portfolio_desc = st.text_input("📄 组合描述", placeholder="例如：以银行股为主的稳健投资组合")
    
    st.markdown("#### 📊 选择股票和权重")
    
    # 股票选择和权重设置
    selected_stocks = {}
    total_weight = 0
    
    # 使用会话状态存储组合数据
    if 'portfolio_stocks' not in st.session_state:
        st.session_state.portfolio_stocks = []
    
    # 添加股票按钮
    col1, col2 = st.columns([3, 1])
    
    with col1:
        stock_options = [f"{stock.stock_code} - {stock.stock_name}" for stock in stocks]
        new_stock = st.selectbox("🎯 选择股票", options=stock_options, key="new_stock_select")

    with col2:
        if st.button("➕ 添加股票", use_container_width=True):
            if new_stock and new_stock not in [item['stock'] for item in st.session_state.portfolio_stocks]:
                st.session_state.portfolio_stocks.append({
                    'stock': new_stock,
                    'weight': 20.0  # 默认权重20%
                })
                st.rerun()
    
    # 显示已选择的股票和权重设置
    if st.session_state.portfolio_stocks:
        st.markdown("#### 📋 组合构成")
        
        for i, item in enumerate(st.session_state.portfolio_stocks):
            col1, col2, col3 = st.columns([3, 2, 1])
            
            with col1:
                st.text(item['stock'])
            
            with col2:
                weight = st.number_input(
                    f"⚖️ 权重 (%)",
                    min_value=0.0,
                    max_value=100.0,
                    value=item['weight'],
                    step=1.0,
                    key=f"weight_{i}"
                )
                st.session_state.portfolio_stocks[i]['weight'] = weight
                total_weight += weight

            with col3:
                if st.button("🗑️ 删除", key=f"remove_{i}"):
                    st.session_state.portfolio_stocks.pop(i)
                    st.rerun()
        
        # 显示总权重
        if total_weight != 100:
            st.warning(f"⚠️ 总权重为 {total_weight:.1f}%，建议调整为100%")
        else:
            st.success(f"✅ 总权重为 {total_weight:.1f}%")
        
        # 创建组合按钮
        if st.button("🚀 创建投资组合", type="primary", use_container_width=True):
            if portfolio_name and len(st.session_state.portfolio_stocks) >= 2:
                if abs(total_weight - 100) < 0.1:  # 允许小的误差
                    st.success("🎉 投资组合创建成功！")
                    # 这里可以保存到数据库
                    st.balloons()
                else:
                    st.error("❌ 请确保总权重为100%")
            else:
                st.error("❌ 请填写组合名称并至少选择2只股票")
    
    # 清空组合按钮
    if st.session_state.portfolio_stocks:
        if st.button("🧹 清空组合"):
            st.session_state.portfolio_stocks = []
            st.rerun()

def analyze_portfolio_tab(stock_manager: StockManager, stocks):
    """分析投资组合选项卡"""
    
    st.subheader("📊 投资组合分析")
    
    # 使用示例组合或用户创建的组合
    if 'portfolio_stocks' in st.session_state and st.session_state.portfolio_stocks:
        portfolio_data = st.session_state.portfolio_stocks
        st.info("正在分析您创建的投资组合")
    else:
        # 使用默认示例组合
        portfolio_data = [
            {'stock': '000001 - 平安银行', 'weight': 20.0},
            {'stock': '600036 - 招商银行', 'weight': 20.0},
            {'stock': '600000 - 浦发银行', 'weight': 20.0},
            {'stock': '000002 - 万科A', 'weight': 20.0},
            {'stock': '000858 - 五粮液', 'weight': 20.0}
        ]
        st.info("正在分析示例投资组合（5只股票等权重）")
    
    # 解析组合数据
    portfolio_stocks = {}
    for item in portfolio_data:
        stock_code = item['stock'].split(' - ')[0]
        stock_name = item['stock'].split(' - ')[1]
        weight = item['weight'] / 100  # 转换为小数
        portfolio_stocks[f"{stock_code} - {stock_name}"] = weight
    
    # 显示组合构成饼图
    st.markdown("#### 🥧 组合构成分布")
    pie_fig = create_portfolio_pie_chart(
        {k: v*100 for k, v in portfolio_stocks.items()},
        "投资组合权重分布"
    )
    st.plotly_chart(pie_fig, use_container_width=True)

    # 计算组合指标
    calculate_portfolio_metrics(stock_manager, portfolio_stocks)

def calculate_portfolio_metrics(stock_manager: StockManager, portfolio_stocks):
    """计算投资组合指标"""

    st.markdown("#### 📈 组合风险收益指标")

    # 获取所有股票的收益率数据
    portfolio_returns = {}
    portfolio_metrics = {}

    for stock_info, weight in portfolio_stocks.items():
        stock_code = stock_info.split(' - ')[0]

        # 获取股票收益率指标
        returns = stock_manager.calculate_returns(stock_code, 365)  # 1年期数据
        portfolio_returns[stock_code] = returns
        portfolio_metrics[stock_info] = {
            'weight': weight * 100,
            'annual_return': returns.get('annual_return', 0),
            'volatility': returns.get('volatility', 0),
            'sharpe_ratio': returns.get('sharpe_ratio', 0),
            'max_drawdown': returns.get('max_drawdown', 0)
        }

    # 计算组合加权指标
    weighted_return = sum(metrics['annual_return'] * metrics['weight'] / 100
                         for metrics in portfolio_metrics.values())

    weighted_volatility = np.sqrt(sum((metrics['volatility'] * metrics['weight'] / 100) ** 2
                                     for metrics in portfolio_metrics.values()))

    # 计算组合夏普比率
    risk_free_rate = 3.0  # 假设无风险利率为3%
    portfolio_sharpe = (weighted_return - risk_free_rate) / weighted_volatility if weighted_volatility > 0 else 0

    # 显示组合整体指标
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("组合年化收益率", format_percentage(weighted_return))

    with col2:
        st.metric("组合年化波动率", format_percentage(weighted_volatility))

    with col3:
        st.metric("组合夏普比率", f"{portfolio_sharpe:.2f}")

    with col4:
        # 计算组合最大回撤（简化计算）
        avg_drawdown = sum(metrics['max_drawdown'] * metrics['weight'] / 100
                          for metrics in portfolio_metrics.values())
        st.metric("预期最大回撤", format_percentage(avg_drawdown))

    # 显示个股贡献分析
    st.markdown("#### 📊 个股贡献分析")

    contribution_data = []
    for stock_info, metrics in portfolio_metrics.items():
        contribution_data.append({
            '股票': stock_info,
            '权重': format_percentage(metrics['weight']),
            '年化收益率': format_percentage(metrics['annual_return']),
            '收益贡献': format_percentage(metrics['annual_return'] * metrics['weight'] / 100),
            '波动率': format_percentage(metrics['volatility']),
            '夏普比率': f"{metrics['sharpe_ratio']:.2f}",
            '最大回撤': format_percentage(metrics['max_drawdown'])
        })

    contribution_df = pd.DataFrame(contribution_data)
    st.dataframe(contribution_df, use_container_width=True, hide_index=True)

    # 风险分散化分析
    st.markdown("#### ⚖️ 风险分散化分析")

    # 计算行业分布
    industry_weights = {}
    for stock_info, weight in portfolio_stocks.items():
        stock_code = stock_info.split(' - ')[0]
        stock = stock_manager.get_stock_by_code(stock_code)
        if stock:
            industry = stock.industry or "其他"
            industry_weights[industry] = industry_weights.get(industry, 0) + weight * 100

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("**行业分布:**")
        for industry, weight in industry_weights.items():
            st.write(f"• {industry}: {weight:.1f}%")

    with col2:
        # 分散化建议
        max_weight = max(portfolio_stocks.values()) * 100
        num_stocks = len(portfolio_stocks)

        st.markdown("**分散化评估:**")
        if num_stocks >= 5 and max_weight <= 30:
            st.success("✅ 组合分散化程度良好")
        elif num_stocks >= 3:
            st.info("ℹ️ 组合具有一定分散化")
        else:
            st.warning("⚠️ 建议增加股票数量以提高分散化")

def portfolio_performance_tab(stock_manager: StockManager):
    """投资组合历史表现选项卡"""

    st.subheader("📈 投资组合历史表现")

    # 时间范围选择
    col1, col2 = st.columns(2)

    with col1:
        period = st.selectbox(
            "选择分析周期",
            options=["1个月", "3个月", "6个月", "1年"],
            index=2,
            key="performance_period"
        )

    with col2:
        benchmark = st.selectbox(
            "选择基准",
            options=["等权重组合", "市值加权", "自定义基准"],
            index=0
        )

    # 使用示例组合数据
    portfolio_data = [
        {'stock': '000001 - 平安银行', 'weight': 20.0},
        {'stock': '600036 - 招商银行', 'weight': 20.0},
        {'stock': '600000 - 浦发银行', 'weight': 20.0},
        {'stock': '000002 - 万科A', 'weight': 20.0},
        {'stock': '000858 - 五粮液', 'weight': 20.0}
    ]

    # 计算时间范围
    end_date = date.today()
    if period == "1个月":
        start_date = end_date - timedelta(days=30)
    elif period == "3个月":
        start_date = end_date - timedelta(days=90)
    elif period == "6个月":
        start_date = end_date - timedelta(days=180)
    else:  # 1年
        start_date = end_date - timedelta(days=365)

    # 模拟组合历史表现
    simulate_portfolio_performance(stock_manager, portfolio_data, start_date, end_date)

def simulate_portfolio_performance(stock_manager: StockManager, portfolio_data, start_date, end_date):
    """模拟投资组合历史表现"""

    st.markdown("#### 📊 组合净值走势")

    # 获取组合中第一只股票的数据作为示例
    first_stock_code = portfolio_data[0]['stock'].split(' - ')[0]
    df = stock_manager.get_stock_data(first_stock_code, start_date, end_date)

    if not df.empty:
        # 模拟组合净值（简化计算）
        df['portfolio_nav'] = df['close_price'] / df['close_price'].iloc[0]

        # 创建净值走势图
        fig = create_line_chart(df, 'portfolio_nav', "投资组合净值走势")
        st.plotly_chart(fig, use_container_width=True)

        # 计算表现指标
        total_return = (df['portfolio_nav'].iloc[-1] - 1) * 100

        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("期间收益率", format_percentage(total_return))

        with col2:
            # 计算最大回撤
            cummax = df['portfolio_nav'].cummax()
            drawdown = (df['portfolio_nav'] - cummax) / cummax
            max_dd = drawdown.min() * 100
            st.metric("最大回撤", format_percentage(max_dd))

        with col3:
            # 计算波动率
            daily_returns = df['portfolio_nav'].pct_change().dropna()
            volatility = daily_returns.std() * np.sqrt(252) * 100
            st.metric("年化波动率", format_percentage(volatility))

    else:
        st.warning("暂无足够的历史数据进行分析")

    # 显示收益率分解
    st.markdown("#### 📈 收益率分解分析")

    returns_data = {}
    periods = ["1周", "1个月", "3个月", "6个月"]

    for item in portfolio_data:
        stock_code = item['stock'].split(' - ')[0]
        stock_name = item['stock'].split(' - ')[1]

        # 模拟不同周期的收益率
        returns_data[f"{stock_code}"] = [
            np.random.normal(2, 5),   # 1周
            np.random.normal(5, 10),  # 1个月
            np.random.normal(8, 15),  # 3个月
            np.random.normal(12, 20)  # 6个月
        ]

    # 创建收益率对比图
    comparison_fig = create_returns_comparison_chart(returns_data, periods, "各股票收益率对比")
    st.plotly_chart(comparison_fig, use_container_width=True)
