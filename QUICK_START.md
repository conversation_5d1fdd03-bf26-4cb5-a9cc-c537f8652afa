# 🚀 快速启动指南

## Windows环境下启动股票分析应用

### 📋 前提条件确认

在启动应用前，请确认以下条件已满足：

✅ **conda环境**: 已创建并激活名为"stock"的conda环境  
✅ **依赖包**: 已安装requirements.txt中的所有依赖包  
✅ **数据库**: MySQL服务已启动，数据库已初始化  
✅ **配置**: 数据库配置为root/123456，数据库名stock_analysis  

### 🎯 启动方法（推荐顺序）

#### 方法一：使用Windows专用启动脚本（推荐）

```bash
# 1. 激活conda环境
conda activate stock

# 2. 运行Windows启动脚本
python scripts/start_windows.py
```

**特点：**
- 自动检查环境和依赖
- 自动打开浏览器
- 详细的状态提示
- 友好的错误处理

#### 方法二：使用批处理文件（最简单）

直接双击项目根目录下的 `start_app.bat` 文件

**特点：**
- 一键启动
- 自动激活conda环境
- 适合非技术用户

#### 方法三：使用简化启动脚本

```bash
# 1. 激活conda环境
conda activate stock

# 2. 运行简化脚本
python scripts/run_streamlit.py
```

#### 方法四：直接使用Streamlit命令

```bash
# 1. 激活conda环境
conda activate stock

# 2. 直接启动
streamlit run app/main.py --server.port=8501 --server.address=localhost
```

### 🌐 访问应用

应用启动成功后：

1. **访问地址**: http://localhost:8501
2. **演示账户**: 
   - 用户名: `admin`
   - 密码: `admin123`

### 🔧 常见问题解决

#### 问题1：地址无效错误 (ERR_ADDRESS_INVALID)

**原因**: 使用了0.0.0.0地址  
**解决**: 使用localhost地址访问

```bash
# 错误地址
http://0.0.0.0:8501

# 正确地址  
http://localhost:8501
```

#### 问题2：端口被占用

**现象**: 启动时提示端口8501被占用  
**解决**: 
```bash
# 查找占用端口的进程
netstat -ano | findstr :8501

# 结束进程（替换PID为实际进程ID）
taskkill /PID <PID> /F

# 或使用其他端口启动
streamlit run app/main.py --server.port=8502 --server.address=localhost
```

#### 问题3：数据库连接失败

**检查步骤**:
1. 确认MySQL服务已启动
2. 测试数据库连接：
```bash
python scripts/test_db_connection.py
```
3. 如果数据库未初始化：
```bash
python scripts/init_db.py
```

#### 问题4：依赖包缺失

**解决**:
```bash
# 重新安装依赖
pip install -r requirements.txt

# 或使用conda安装
conda install --file requirements.txt
```

### 📊 应用功能导航

启动成功后，您可以使用以下功能：

1. **🏠 仪表板**
   - 查看市场概览
   - 热门股票排行
   - 行业分布统计

2. **📈 股票分析**
   - 选择股票进行分析
   - 查看K线图和价格走势
   - 分析收益率和风险指标

3. **💼 投资组合分析**
   - 创建投资组合
   - 设置股票权重
   - 分析组合风险收益

### 🛑 停止应用

在命令行窗口中按 `Ctrl + C` 停止应用

### 📞 获取帮助

如果遇到问题：

1. 查看终端输出的错误信息
2. 运行诊断脚本：`python scripts/verify_setup.py`
3. 检查日志文件（如果有）
4. 参考项目README.md文档

### 💡 性能优化建议

1. **首次启动较慢**: 正常现象，后续启动会更快
2. **内存使用**: 建议至少4GB可用内存
3. **浏览器**: 推荐使用Chrome或Edge浏览器
4. **网络**: 确保防火墙未阻止localhost:8501
