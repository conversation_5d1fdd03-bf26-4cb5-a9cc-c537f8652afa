# 📚 API 文档

股票分析系统内部API和数据模型文档。

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Streamlit     │    │   Business      │    │   Database      │
│   Frontend      │◄──►│   Logic Layer   │◄──►│   Layer         │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📊 数据模型

### User 模型

```python
@dataclass
class User:
    id: Optional[int] = None
    username: str = ""
    password_hash: str = ""
    email: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_active: bool = True
```

**字段说明:**
- `id`: 用户唯一标识
- `username`: 用户名（唯一）
- `password_hash`: 加密后的密码
- `email`: 邮箱地址
- `created_at`: 创建时间
- `updated_at`: 更新时间
- `is_active`: 是否激活

### Stock 模型

```python
@dataclass
class Stock:
    id: Optional[int] = None
    stock_code: str = ""
    stock_name: str = ""
    market: str = ""
    industry: Optional[str] = None
    sector: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_active: bool = True
```

**字段说明:**
- `stock_code`: 股票代码（如：000001）
- `stock_name`: 股票名称（如：平安银行）
- `market`: 交易市场（SH/SZ/BJ）
- `industry`: 所属行业
- `sector`: 所属板块

### StockData 模型

```python
@dataclass
class StockData:
    id: Optional[int] = None
    stock_code: str = ""
    trade_date: date = None
    open_price: float = 0.0
    high_price: float = 0.0
    low_price: float = 0.0
    close_price: float = 0.0
    volume: int = 0
    amount: float = 0.0
    turnover_rate: float = 0.0
    created_at: Optional[datetime] = None
```

## 🔧 核心服务类

### UserManager

用户管理服务类，提供用户相关的所有操作。

#### 方法列表

##### `create_user(username: str, password: str, email: str = None) -> bool`

创建新用户。

**参数:**
- `username`: 用户名
- `password`: 明文密码
- `email`: 邮箱地址（可选）

**返回值:**
- `bool`: 创建成功返回 True，失败返回 False

**示例:**
```python
user_manager = UserManager()
success = user_manager.create_user("newuser", "password123", "<EMAIL>")
```

##### `authenticate_user(username: str, password: str) -> Optional[User]`

验证用户登录。

**参数:**
- `username`: 用户名
- `password`: 明文密码

**返回值:**
- `Optional[User]`: 验证成功返回用户对象，失败返回 None

##### `get_user_by_username(username: str) -> Optional[User]`

根据用户名获取用户信息。

### StockManager

股票数据管理服务类。

#### 方法列表

##### `get_all_stocks() -> List[Stock]`

获取所有活跃股票列表。

**返回值:**
- `List[Stock]`: 股票对象列表

##### `get_stock_by_code(stock_code: str) -> Optional[Stock]`

根据股票代码获取股票信息。

**参数:**
- `stock_code`: 股票代码

**返回值:**
- `Optional[Stock]`: 股票对象或 None

##### `get_stock_data(stock_code: str, start_date: date = None, end_date: date = None) -> pd.DataFrame`

获取股票历史数据。

**参数:**
- `stock_code`: 股票代码
- `start_date`: 开始日期（可选）
- `end_date`: 结束日期（可选）

**返回值:**
- `pd.DataFrame`: 股票历史数据

##### `calculate_returns(stock_code: str, period_days: int = 30) -> Dict[str, float]`

计算股票收益率指标。

**参数:**
- `stock_code`: 股票代码
- `period_days`: 分析周期（天数）

**返回值:**
- `Dict[str, float]`: 包含各种收益率指标的字典

**返回字段:**
```python
{
    'total_return': float,      # 总收益率 (%)
    'annual_return': float,     # 年化收益率 (%)
    'volatility': float,        # 年化波动率 (%)
    'sharpe_ratio': float,      # 夏普比率
    'max_drawdown': float       # 最大回撤 (%)
}
```

## 🛠️ 工具函数

### helpers.py

#### 格式化函数

##### `format_number(value: float, decimal_places: int = 2) -> str`

格式化数字显示（支持万、亿单位）。

##### `format_percentage(value: float, decimal_places: int = 2) -> str`

格式化百分比显示。

##### `format_currency(value: float, currency: str = "¥") -> str`

格式化货币显示。

#### 计算函数

##### `calculate_period_return(prices: pd.Series, period: str = "1M") -> float`

计算指定期间的收益率。

**支持的周期:**
- "1D": 1天
- "1W": 1周
- "1M": 1个月
- "3M": 3个月
- "6M": 6个月
- "1Y": 1年
- "YTD": 年初至今

#### 图表函数

##### `create_candlestick_chart(df: pd.DataFrame, title: str) -> go.Figure`

创建K线图。

##### `create_line_chart(df: pd.DataFrame, y_column: str, title: str) -> go.Figure`

创建折线图。

##### `create_portfolio_pie_chart(holdings: Dict[str, float], title: str) -> go.Figure`

创建投资组合饼图。

## 🗄️ 数据库架构

### 表结构

#### users 表
```sql
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);
```

#### stocks 表
```sql
CREATE TABLE stocks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    stock_code VARCHAR(20) UNIQUE NOT NULL,
    stock_name VARCHAR(100) NOT NULL,
    market VARCHAR(20) NOT NULL,
    industry VARCHAR(50),
    sector VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);
```

#### stock_data 表
```sql
CREATE TABLE stock_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    stock_code VARCHAR(20) NOT NULL,
    trade_date DATE NOT NULL,
    open_price DECIMAL(10,3) NOT NULL,
    high_price DECIMAL(10,3) NOT NULL,
    low_price DECIMAL(10,3) NOT NULL,
    close_price DECIMAL(10,3) NOT NULL,
    volume BIGINT DEFAULT 0,
    amount DECIMAL(15,2) DEFAULT 0,
    turnover_rate DECIMAL(8,4) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_stock_date (stock_code, trade_date)
);
```

### 索引策略

```sql
-- 股票数据查询优化
CREATE INDEX idx_stock_data_code ON stock_data(stock_code);
CREATE INDEX idx_stock_data_date ON stock_data(trade_date);
CREATE INDEX idx_stock_data_code_date ON stock_data(stock_code, trade_date);

-- 用户查询优化
CREATE INDEX idx_username ON users(username);
```

## 🔐 安全机制

### 密码加密

使用 bcrypt 进行密码哈希：

```python
import bcrypt

# 加密密码
password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())

# 验证密码
is_valid = bcrypt.checkpw(password.encode('utf-8'), stored_hash.encode('utf-8'))
```

### 会话管理

使用 Streamlit 的 session_state 管理用户会话：

```python
# 登录状态
st.session_state.logged_in = True
st.session_state.username = user.username
st.session_state.user_id = user.id

# 登出
st.session_state.logged_in = False
st.session_state.username = None
st.session_state.user_id = None
```

## 📈 性能优化

### 数据库查询优化

1. **使用索引**: 为常用查询字段添加索引
2. **分页查询**: 大数据量时使用 LIMIT 分页
3. **连接池**: 使用数据库连接池减少连接开销

### 缓存策略

```python
# 使用 Streamlit 缓存
@st.cache_data
def get_stock_data(stock_code: str, days: int = 30):
    # 缓存股票数据查询结果
    pass
```

## 🧪 测试指南

### 单元测试

```python
# 测试用户认证
def test_user_authentication():
    user_manager = UserManager()
    user = user_manager.authenticate_user("admin", "admin123")
    assert user is not None
    assert user.username == "admin"
```

### 集成测试

```python
# 测试完整的数据流
def test_stock_analysis_flow():
    stock_manager = StockManager()
    stocks = stock_manager.get_all_stocks()
    assert len(stocks) > 0
    
    stock_data = stock_manager.get_stock_data(stocks[0].stock_code)
    assert not stock_data.empty
```

## 📝 开发规范

### 代码风格

- 遵循 PEP 8 规范
- 使用类型提示
- 编写详细的文档字符串
- 适当的错误处理

### 提交规范

```
feat: 添加新功能
fix: 修复bug
docs: 更新文档
style: 代码格式调整
refactor: 代码重构
test: 添加测试
chore: 构建过程或辅助工具的变动
```
