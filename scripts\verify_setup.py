"""
配置验证脚本
Configuration Verification Script

验证所有数据库配置是否正确更新
"""

import os
import sys
from pathlib import Path

def check_file_content(file_path, expected_content, description):
    """检查文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        if expected_content in content:
            print(f"✅ {description}: 配置正确")
            return True
        else:
            print(f"❌ {description}: 配置错误")
            return False
    except FileNotFoundError:
        print(f"❌ {description}: 文件不存在 - {file_path}")
        return False
    except Exception as e:
        print(f"❌ {description}: 检查失败 - {e}")
        return False

def verify_database_config():
    """验证数据库配置"""
    print("🔍 验证数据库配置更新...")
    print("=" * 50)
    
    checks = []
    
    # 检查 app/database/connection.py
    checks.append(check_file_content(
        "app/database/connection.py",
        "'password': os.getenv('DB_PASSWORD', '123456')",
        "数据库连接模块"
    ))
    
    # 检查 config/config.py
    checks.append(check_file_content(
        "config/config.py",
        "DB_USER = os.getenv('DB_USER', 'root')",
        "应用配置文件"
    ))
    
    checks.append(check_file_content(
        "config/config.py",
        "DB_PASSWORD = os.getenv('DB_PASSWORD', '123456')",
        "应用配置文件密码"
    ))
    
    # 检查 docker-compose.yml
    checks.append(check_file_content(
        "docker-compose.yml",
        "MYSQL_ROOT_PASSWORD: 123456",
        "Docker Compose MySQL配置"
    ))
    
    checks.append(check_file_content(
        "docker-compose.yml",
        "DB_USER: root",
        "Docker Compose应用配置"
    ))
    
    checks.append(check_file_content(
        "docker-compose.yml",
        "DB_PASSWORD: 123456",
        "Docker Compose应用密码配置"
    ))
    
    # 检查 .env.example
    checks.append(check_file_content(
        ".env.example",
        "DB_USER=root",
        "环境变量示例文件"
    ))
    
    checks.append(check_file_content(
        ".env.example",
        "DB_PASSWORD=123456",
        "环境变量示例文件密码"
    ))
    
    # 检查 .env
    checks.append(check_file_content(
        ".env",
        "DB_USER=root",
        "环境变量文件"
    ))
    
    checks.append(check_file_content(
        ".env",
        "DB_PASSWORD=123456",
        "环境变量文件密码"
    ))
    
    return all(checks)

def verify_file_structure():
    """验证文件结构"""
    print("\n🔍 验证文件结构...")
    print("=" * 50)
    
    required_files = [
        "app/database/connection.py",
        "config/config.py",
        "docker-compose.yml",
        ".env.example",
        ".env",
        "scripts/test_db_connection.py",
        "scripts/init_db.py"
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - 文件不存在")
            all_exist = False
    
    return all_exist

def show_next_steps():
    """显示后续步骤"""
    print("\n📋 后续步骤:")
    print("=" * 50)
    print("1. 确保MySQL服务已启动")
    print("2. 运行数据库连接测试:")
    print("   python scripts/test_db_connection.py")
    print("3. 初始化数据库:")
    print("   python scripts/init_db.py")
    print("4. 启动应用:")
    print("   python scripts/start.py")
    print("5. 访问应用:")
    print("   http://localhost:8501")
    print("6. 使用演示账户登录:")
    print("   用户名: admin")
    print("   密码: admin123")

def main():
    """主函数"""
    print("🚀 股票分析系统配置验证")
    print("=" * 60)
    
    # 验证数据库配置
    config_ok = verify_database_config()
    
    # 验证文件结构
    structure_ok = verify_file_structure()
    
    print("\n📊 验证结果:")
    print("=" * 50)
    
    if config_ok and structure_ok:
        print("✅ 所有配置验证通过！")
        print("🎉 数据库配置已成功更新为:")
        print("   - 主机: localhost")
        print("   - 端口: 3306")
        print("   - 用户: root")
        print("   - 密码: 123456")
        print("   - 数据库: stock_analysis")
        
        show_next_steps()
    else:
        print("❌ 配置验证失败，请检查上述错误")
        sys.exit(1)

if __name__ == "__main__":
    main()
