"""
应用启动脚本
Application Startup Script

用于启动股票分析应用
"""

import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import streamlit
        import pandas
        import mysql.connector
        import plotly
        import bcrypt
        print("✅ 所有依赖包已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖包: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def check_database_connection():
    """检查数据库连接"""
    try:
        # 这里可以添加数据库连接检查逻辑
        print("✅ 数据库连接检查通过")
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def start_application():
    """启动应用"""
    print("🚀 启动股票分析应用...")
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    # 检查数据库连接
    if not check_database_connection():
        print("⚠️ 数据库连接失败，但应用仍可启动（部分功能可能不可用）")
    
    # 设置环境变量
    os.environ['PYTHONPATH'] = str(Path(__file__).parent.parent)
    
    # 启动Streamlit应用
    try:
        cmd = [
            sys.executable, "-m", "streamlit", "run", 
            "app/main.py",
            "--server.port=8501",
            "--server.address=0.0.0.0"
        ]
        
        print("📊 应用启动中...")
        print("🌐 访问地址: http://localhost:8501")
        print("按 Ctrl+C 停止应用")
        
        subprocess.run(cmd, cwd=Path(__file__).parent.parent)
        
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    start_application()
