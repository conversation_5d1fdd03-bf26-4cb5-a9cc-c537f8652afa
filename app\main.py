"""
股票分析应用主程序
Stock Analysis Application Main Module

这是基于Streamlit的股票分析应用的主入口文件
"""

import streamlit as st
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.auth.login import login_page
from app.views.dashboard import dashboard_page
from app.views.stock_analysis import stock_analysis_page
from app.views.portfolio_analysis import portfolio_analysis_page
from app.database.connection import init_database

def hide_streamlit_navigation():
    """隐藏Streamlit默认的页面导航和修复JavaScript错误"""
    st.markdown("""
    <style>
        /* 隐藏默认的页面导航 */
        .css-1d391kg {display: none;}
        .css-1rs6os {display: none;}
        .css-17ziqus {display: none;}

        /* 隐藏页面选择器 */
        [data-testid="stSidebarNav"] {display: none;}
        section[data-testid="stSidebarNav"] {display: none;}

        /* 隐藏Deploy按钮和菜单 */
        .css-1rs6os .css-1cpxqw2 {display: none;}
        [data-testid="stToolbar"] {display: none;}

        /* 隐藏主菜单 */
        #MainMenu {display: none;}

        /* 隐藏页脚 */
        footer {display: none;}

        /* 隐藏header */
        header {display: none;}

        /* 自定义侧边栏样式 */
        .css-1d391kg {
            padding-top: 1rem;
        }

        /* 修复可能的样式冲突 */
        .stApp > div:first-child {
            display: none;
        }

        /* 确保内容区域正常显示 */
        .main .block-container {
            padding-top: 1rem;
        }
    </style>

    <script>
        // 修复可能的JavaScript错误
        window.addEventListener('error', function(e) {
            if (e.message && e.message.includes('shadowRoot')) {
                e.preventDefault();
                return false;
            }
        });

        // 防止热键错误
        document.addEventListener('keydown', function(e) {
            if (e.key === undefined || e.key === null) {
                e.preventDefault();
                return false;
            }
        });
    </script>
    """, unsafe_allow_html=True)

def show_help_info():
    """显示帮助信息"""
    st.info("""
    📖 **使用指南**

    **📊 市场概览**
    - 查看市场整体情况
    - 浏览热门股票排行
    - 了解行业分布统计

    **📈 股票分析**
    - 选择股票进行详细分析
    - 查看价格走势和技术指标
    - 分析收益率和风险指标

    **💼 投资组合分析**
    - 创建个人投资组合
    - 设置股票权重分配
    - 分析组合风险收益

    **💡 小贴士**
    - 点击图表可以交互操作
    - 使用"刷新数据"更新最新信息
    - 建议定期查看投资组合表现
    """)

def main():
    """主应用程序入口"""

    # 设置页面配置
    st.set_page_config(
        page_title="📈 股票分析系统",
        page_icon="📈",
        layout="wide",
        initial_sidebar_state="expanded",
        menu_items={
            'Get Help': None,
            'Report a bug': None,
            'About': "📈 股票分析系统 v1.0.0\n\n专业的股票数据分析平台"
        }
    )

    # 隐藏Streamlit默认的页面导航
    hide_streamlit_navigation()

    # 初始化数据库连接
    init_database()
    
    # 检查用户是否已登录
    if 'logged_in' not in st.session_state:
        st.session_state.logged_in = False
    
    if not st.session_state.logged_in:
        # 显示登录页面
        login_page()
    else:
        # 显示主应用界面
        show_main_app()

def show_main_app():
    """显示主应用界面"""

    # 初始化当前页面状态
    if 'current_page' not in st.session_state:
        st.session_state.current_page = "📊 市场概览"

    # 侧边栏设计
    with st.sidebar:
        # 系统标题
        st.markdown("""
        <div style="text-align: center; padding: 1rem 0;">
            <h2>📈 股票分析系统</h2>
            <p style="color: #666; font-size: 0.9rem;">专业投资分析平台</p>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("---")

        # 用户信息卡片
        if 'username' in st.session_state:
            st.markdown(f"""
            <div style="background: linear-gradient(90deg, #4CAF50, #45a049);
                        padding: 1rem; border-radius: 10px; margin-bottom: 1rem;">
                <div style="color: white; text-align: center;">
                    <div style="font-size: 1.2rem;">👤 {st.session_state.username}</div>
                    <div style="font-size: 0.9rem; opacity: 0.9;">欢迎回来！</div>
                </div>
            </div>
            """, unsafe_allow_html=True)

        # 主要功能导航
        st.markdown("### 🧭 主要功能")

        # 使用按钮组代替下拉菜单
        col1, col2 = st.columns(2)

        with col1:
            if st.button("📊 市场概览", use_container_width=True,
                        type="primary" if st.session_state.current_page == "📊 市场概览" else "secondary"):
                st.session_state.current_page = "📊 市场概览"
                st.rerun()

        with col2:
            if st.button("📈 股票分析", use_container_width=True,
                        type="primary" if st.session_state.current_page == "📈 股票分析" else "secondary"):
                st.session_state.current_page = "📈 股票分析"
                st.rerun()

        if st.button("💼 投资组合分析", use_container_width=True,
                    type="primary" if st.session_state.current_page == "💼 投资组合分析" else "secondary"):
            st.session_state.current_page = "💼 投资组合分析"
            st.rerun()

        st.markdown("---")

        # 工具和设置
        st.markdown("### ⚙️ 工具设置")

        # 快捷功能
        if st.button("🔄 刷新数据", use_container_width=True):
            st.cache_data.clear()
            st.success("✅ 数据已刷新")
            st.rerun()

        if st.button("📋 使用帮助", use_container_width=True):
            show_help_info()

        st.markdown("---")

        # 退出登录
        if st.button("🚪 退出登录", use_container_width=True, type="secondary"):
            st.session_state.logged_in = False
            st.session_state.username = None
            st.session_state.current_page = None
            st.rerun()

        # 版本信息
        st.markdown("""
        <div style="text-align: center; margin-top: 2rem; color: #888; font-size: 0.8rem;">
            <p>版本 1.0.0</p>
            <p>© 2024 股票分析系统</p>
        </div>
        """, unsafe_allow_html=True)

    # 根据当前页面显示内容
    if st.session_state.current_page == "📊 市场概览":
        dashboard_page()
    elif st.session_state.current_page == "📈 股票分析":
        stock_analysis_page()
    elif st.session_state.current_page == "💼 投资组合分析":
        portfolio_analysis_page()
    else:
        # 默认显示仪表板
        dashboard_page()

if __name__ == "__main__":
    main()
