"""
股票分析应用主程序
Stock Analysis Application Main Module

这是基于Streamlit的股票分析应用的主入口文件
"""

import streamlit as st
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.auth.login import login_page
from app.pages.dashboard import dashboard_page
from app.pages.stock_analysis import stock_analysis_page
from app.pages.portfolio_analysis import portfolio_analysis_page
from app.database.connection import init_database

def main():
    """主应用程序入口"""
    
    # 设置页面配置
    st.set_page_config(
        page_title="股票分析系统",
        page_icon="📈",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # 初始化数据库连接
    init_database()
    
    # 检查用户是否已登录
    if 'logged_in' not in st.session_state:
        st.session_state.logged_in = False
    
    if not st.session_state.logged_in:
        # 显示登录页面
        login_page()
    else:
        # 显示主应用界面
        show_main_app()

def show_main_app():
    """显示主应用界面"""
    
    # 侧边栏导航
    st.sidebar.title("📈 股票分析系统")
    st.sidebar.markdown("---")
    
    # 显示当前用户
    if 'username' in st.session_state:
        st.sidebar.success(f"欢迎, {st.session_state.username}!")
    
    # 导航菜单
    page = st.sidebar.selectbox(
        "🧭 选择功能模块",
        ["📊 仪表板", "📈 股票分析", "💼 投资组合分析"]
    )
    
    # 登出按钮
    if st.sidebar.button("🚪 退出登录"):
        st.session_state.logged_in = False
        st.session_state.username = None
        st.rerun()
    
    # 根据选择显示对应页面
    if page == "📊 仪表板":
        dashboard_page()
    elif page == "📈 股票分析":
        stock_analysis_page()
    elif page == "💼 投资组合分析":
        portfolio_analysis_page()

if __name__ == "__main__":
    main()
