"""
股票分析页面
Stock Analysis Page

提供单只股票的详细分析功能
"""

import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from app.models.stock import StockManager
from app.utils.helpers import (
    format_number, format_percentage, format_currency,
    display_metric_cards, create_candlestick_chart, create_line_chart,
    validate_date_range, calculate_period_return
)

def stock_analysis_page():
    """股票分析主页面"""
    
    # 页面标题
    st.markdown("""
    <div style="text-align: center; padding: 2rem 0 1rem 0;">
        <h1>📈 股票深度分析</h1>
        <p style="color: #666; font-size: 1.1rem;">专业的股票技术分析和投资决策支持</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 初始化股票管理器
    stock_manager = StockManager()
    
    # 获取所有股票列表
    stocks = stock_manager.get_all_stocks()
    
    if not stocks:
        st.error("⚠️ 暂无股票数据，请联系管理员检查数据库连接")
        return
    
    # 股票选择器 - 使用更好的布局
    with st.container():
        st.markdown("### 🎯 选择分析标的")
        
        col1, col2 = st.columns([3, 1])
        
        with col1:
            stock_options = [f"{stock.stock_code} - {stock.stock_name}" for stock in stocks]
            selected_stock = st.selectbox(
                "请选择要分析的股票",
                options=stock_options,
                index=0,
                help="选择您要进行深度分析的股票"
            )
        
        with col2:
            st.markdown("<br>", unsafe_allow_html=True)
            if st.button("🔄 刷新股票列表", use_container_width=True):
                st.cache_data.clear()
                st.rerun()
    
    if not selected_stock:
        st.warning("请选择要分析的股票")
        return
    
    # 解析选择的股票
    stock_code = selected_stock.split(' - ')[0]
    stock_name = selected_stock.split(' - ')[1]
    
    # 获取股票对象
    selected_stock_obj = None
    for stock in stocks:
        if stock.stock_code == stock_code:
            selected_stock_obj = stock
            break
    
    if not selected_stock_obj:
        st.error("无法找到选择的股票信息")
        return
    
    # 显示股票基本信息
    show_stock_info(selected_stock_obj, stock_manager)
    
    st.markdown("---")
    
    # 分析参数设置
    show_analysis_controls(stock_manager, stock_code, stock_name)

def show_stock_info(stock: object, stock_manager: StockManager):
    """显示股票基本信息"""
    
    st.markdown("### 📋 股票基本信息")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.info(f"**股票代码**\n{stock.stock_code}")
    
    with col2:
        st.info(f"**股票名称**\n{stock.stock_name}")
    
    with col3:
        st.info(f"**所属行业**\n{stock.industry or 'N/A'}")
    
    with col4:
        st.info(f"**交易市场**\n{stock.market}")
    
    # 获取最新价格和基本指标
    latest_price = stock_manager.get_latest_price(stock.stock_code)
    returns_30d = stock_manager.calculate_returns(stock.stock_code, 30)
    
    # 显示关键指标
    st.markdown("### 📊 关键指标")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "最新价格",
            format_currency(latest_price) if latest_price else "N/A",
            help="最新交易日收盘价"
        )
    
    with col2:
        st.metric(
            "30日收益率",
            format_percentage(returns_30d.get('total_return', 0)),
            help="最近30个交易日的总收益率"
        )
    
    with col3:
        st.metric(
            "年化波动率",
            format_percentage(returns_30d.get('volatility', 0)),
            help="基于30日数据计算的年化波动率"
        )
    
    with col4:
        st.metric(
            "夏普比率",
            f"{returns_30d.get('sharpe_ratio', 0):.2f}",
            help="风险调整后的收益率指标"
        )

def show_analysis_controls(stock_manager: StockManager, stock_code: str, stock_name: str):
    """显示分析控制面板"""
    
    st.markdown("### ⚙️ 分析参数设置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        period = st.selectbox(
            "📅 选择分析周期",
            options=["1个月", "3个月", "6个月", "1年", "自定义"],
            index=2
        )
    
    with col2:
        chart_type = st.selectbox(
            "📊 图表类型",
            options=["K线图", "折线图"],
            index=0
        )
    
    # 计算日期范围
    end_date = date.today()
    
    if period == "1个月":
        start_date = end_date - timedelta(days=30)
    elif period == "3个月":
        start_date = end_date - timedelta(days=90)
    elif period == "6个月":
        start_date = end_date - timedelta(days=180)
    elif period == "1年":
        start_date = end_date - timedelta(days=365)
    else:  # 自定义
        st.markdown("#### 📅 自定义日期范围")
        col1, col2 = st.columns(2)
        with col1:
            start_date = st.date_input("📅 开始日期", value=end_date - timedelta(days=90))
        with col2:
            end_date = st.date_input("📅 结束日期", value=end_date)
        
        if not validate_date_range(start_date, end_date):
            return
    
    # 显示分析结果
    show_analysis_results(stock_manager, stock_code, stock_name, start_date, end_date, chart_type)

def show_analysis_results(stock_manager: StockManager, stock_code: str, stock_name: str, 
                         start_date: date, end_date: date, chart_type: str):
    """显示分析结果"""
    
    # 获取股票数据
    df = stock_manager.get_stock_data(stock_code, start_date, end_date)
    
    if df.empty:
        st.warning(f"在指定时间范围内没有找到 {stock_code} 的数据")
        return
    
    st.markdown("### 📈 价格走势分析")
    
    # 显示图表
    if chart_type == "K线图":
        fig = create_candlestick_chart(df, f"{stock_name} ({stock_code}) K线图")
    else:
        fig = create_line_chart(df, 'close_price', f"{stock_name} ({stock_code}) 价格走势")
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 计算分析期间的收益率指标
    period_days = (end_date - start_date).days
    returns = stock_manager.calculate_returns(stock_code, period_days)
    
    # 显示收益率分析
    st.markdown("### 📊 收益率分析")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric(
            "期间总收益率",
            format_percentage(returns.get('total_return', 0)),
            help=f"{period_days}天期间的总收益率"
        )
    
    with col2:
        st.metric(
            "年化收益率",
            format_percentage(returns.get('annual_return', 0)),
            help="基于期间收益率计算的年化收益率"
        )
    
    with col3:
        st.metric(
            "最大回撤",
            format_percentage(returns.get('max_drawdown', 0)),
            help="期间内的最大回撤幅度"
        )
    
    # 显示风险分析
    st.markdown("### ⚖️ 风险分析")
    
    volatility = returns.get('volatility', 0)
    sharpe_ratio = returns.get('sharpe_ratio', 0)
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.metric(
            "年化波动率",
            format_percentage(volatility),
            help="衡量价格波动程度的指标"
        )
        
        # 风险等级评估
        if volatility < 15:
            risk_level = "🟢 低风险"
        elif volatility < 25:
            risk_level = "🟡 中等风险"
        else:
            risk_level = "🔴 高风险"
        
        st.info(f"**风险等级**: {risk_level}")
    
    with col2:
        st.metric(
            "夏普比率",
            f"{sharpe_ratio:.2f}",
            help="风险调整后的收益率指标"
        )
        
        # 夏普比率评估
        if sharpe_ratio > 1:
            sharpe_assessment = "🟢 优秀"
        elif sharpe_ratio > 0.5:
            sharpe_assessment = "🟡 良好"
        elif sharpe_ratio > 0:
            sharpe_assessment = "🟠 一般"
        else:
            sharpe_assessment = "🔴 较差"
        
        st.info(f"**表现评级**: {sharpe_assessment}")
    
    # 显示数据表格
    st.markdown("### 📋 历史数据")
    
    # 格式化数据表格
    display_df = df.tail(10).copy()  # 显示最近10天数据
    display_df = display_df[['open_price', 'high_price', 'low_price', 'close_price', 'volume']]
    display_df.columns = ['开盘价', '最高价', '最低价', '收盘价', '成交量']
    
    # 格式化价格列
    for col in ['开盘价', '最高价', '最低价', '收盘价']:
        display_df[col] = display_df[col].apply(lambda x: f"¥{x:.2f}")
    
    # 格式化成交量
    display_df['成交量'] = display_df['成交量'].apply(format_number)
    
    st.dataframe(display_df, use_container_width=True)
