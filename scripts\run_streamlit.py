"""
简化的Streamlit启动脚本
Simplified Streamlit Startup Script

直接启动Streamlit应用，适用于已配置好环境的情况
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """主函数"""
    print("🚀 启动股票分析系统...")
    
    # 设置项目根目录
    project_root = Path(__file__).parent.parent
    os.environ['PYTHONPATH'] = str(project_root)
    
    # 切换到项目根目录
    os.chdir(project_root)
    
    # 构建启动命令
    cmd = [
        sys.executable, "-m", "streamlit", "run", 
        "app/main.py",
        "--server.port=8501",
        "--server.address=localhost",
        "--server.headless=false",
        "--browser.gatherUsageStats=false"
    ]
    
    print("📊 应用启动中...")
    print("🌐 访问地址: http://localhost:8501")
    print("👤 演示账户: admin / admin123")
    print("⏹️ 按 Ctrl+C 停止应用")
    print("-" * 50)
    
    try:
        # 启动应用
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")

if __name__ == "__main__":
    main()
