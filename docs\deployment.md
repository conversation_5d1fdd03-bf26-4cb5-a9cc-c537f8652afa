# 📦 部署指南

本文档详细说明如何在不同环境中部署股票分析系统。

## 🐳 Docker 部署（推荐）

### 前置要求
- Docker 20.10+
- Docker Compose 2.0+
- 至少 4GB 可用内存

### 部署步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd stock-analysis-app
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，修改数据库密码等配置
```

3. **启动服务**
```bash
# 后台启动
docker-compose up -d

# 查看日志
docker-compose logs -f

# 检查服务状态
docker-compose ps
```

4. **验证部署**
- 访问 http://localhost:8501
- 使用演示账户登录：admin / admin123

### Docker 服务说明

- **mysql**: MySQL 8.0 数据库服务
  - 端口: 3306
  - 数据持久化: mysql_data 卷
  - 自动初始化数据库和示例数据

- **app**: Streamlit 应用服务
  - 端口: 8501
  - 依赖 MySQL 服务健康检查
  - 支持代码热重载（开发模式）

## 🖥️ 本地开发部署

### 前置要求
- Python 3.11+
- MySQL 8.0+
- Git

### 环境准备

1. **创建虚拟环境**
```bash
# Windows
python -m venv venv
venv\Scripts\activate

# Linux/Mac
python3 -m venv venv
source venv/bin/activate
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

### 数据库配置

1. **安装 MySQL**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server

# CentOS/RHEL
sudo yum install mysql-server

# Windows
# 下载并安装 MySQL Community Server
```

2. **创建数据库和用户**
```sql
mysql -u root -p

CREATE DATABASE stock_analysis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'stock_user'@'localhost' IDENTIFIED BY 'stock_pass';
GRANT ALL PRIVILEGES ON stock_analysis.* TO 'stock_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

3. **初始化数据库**
```bash
python scripts/init_db.py
```

### 启动应用

```bash
# 使用启动脚本
python scripts/start.py

# 或直接使用 streamlit
streamlit run app/main.py --server.port=8501
```

## ☁️ 云服务器部署

### 阿里云 ECS 部署

1. **服务器配置要求**
- CPU: 2核心以上
- 内存: 4GB 以上
- 存储: 20GB 以上
- 操作系统: Ubuntu 20.04 LTS

2. **安装 Docker**
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装 Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

3. **部署应用**
```bash
# 克隆代码
git clone <repository-url>
cd stock-analysis-app

# 配置环境变量
cp .env.example .env
nano .env

# 启动服务
sudo docker-compose up -d
```

4. **配置防火墙**
```bash
# 开放端口
sudo ufw allow 8501
sudo ufw allow 3306  # 如果需要外部访问数据库
sudo ufw enable
```

### 腾讯云 CVM 部署

类似阿里云部署流程，主要差异：
- 使用腾讯云的安全组配置端口
- 可选择使用腾讯云 MySQL 云数据库

## 🔧 生产环境优化

### 性能优化

1. **数据库优化**
```sql
-- 添加索引
CREATE INDEX idx_stock_data_date ON stock_data(trade_date);
CREATE INDEX idx_stock_data_code_date ON stock_data(stock_code, trade_date);

-- 配置 MySQL
# /etc/mysql/mysql.conf.d/mysqld.cnf
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
max_connections = 200
```

2. **应用优化**
```python
# config/production.py
class ProductionConfig(Config):
    DEBUG = False
    STREAMLIT_SERVER_MAX_UPLOAD_SIZE = 200
    STREAMLIT_SERVER_MAX_MESSAGE_SIZE = 200
```

### 安全配置

1. **数据库安全**
```bash
# 运行 MySQL 安全脚本
sudo mysql_secure_installation

# 修改默认密码
ALTER USER 'root'@'localhost' IDENTIFIED BY 'strong_password';
```

2. **应用安全**
```bash
# 设置强密码
export SECRET_KEY=$(openssl rand -hex 32)

# 限制访问
# 使用 nginx 反向代理
# 配置 SSL 证书
```

### 监控和日志

1. **应用监控**
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  app:
    # ... 其他配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

2. **数据库监控**
```sql
-- 启用慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;
```

## 🔄 备份和恢复

### 数据库备份

1. **自动备份脚本**
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/mysql"
DB_NAME="stock_analysis"

mkdir -p $BACKUP_DIR

mysqldump -u stock_user -p$DB_PASSWORD $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

# 保留最近7天的备份
find $BACKUP_DIR -name "backup_*.sql" -mtime +7 -delete
```

2. **定时备份**
```bash
# 添加到 crontab
crontab -e

# 每天凌晨2点备份
0 2 * * * /path/to/backup.sh
```

### 数据恢复

```bash
# 恢复数据库
mysql -u stock_user -p stock_analysis < backup_20240101_020000.sql
```

## 🚨 故障排除

### 常见问题

1. **数据库连接失败**
```bash
# 检查 MySQL 服务状态
sudo systemctl status mysql

# 检查端口占用
netstat -tlnp | grep 3306

# 检查防火墙
sudo ufw status
```

2. **应用启动失败**
```bash
# 查看应用日志
docker-compose logs app

# 检查端口占用
netstat -tlnp | grep 8501

# 重启服务
docker-compose restart app
```

3. **内存不足**
```bash
# 检查内存使用
free -h
docker stats

# 优化 MySQL 配置
# 减少 innodb_buffer_pool_size
```

### 日志分析

```bash
# 查看应用日志
tail -f logs/app.log

# 查看 MySQL 错误日志
sudo tail -f /var/log/mysql/error.log

# 查看 Docker 日志
docker-compose logs -f --tail=100
```

## 📞 技术支持

如遇到部署问题，请：

1. 检查系统要求是否满足
2. 查看错误日志
3. 参考故障排除指南
4. 提交 Issue 并附上详细错误信息
