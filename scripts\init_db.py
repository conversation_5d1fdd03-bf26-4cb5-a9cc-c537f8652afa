"""
数据库初始化脚本
Database Initialization Script

用于初始化数据库和插入示例数据
"""

import mysql.connector
from mysql.connector import Error
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from config.config import get_config

def create_database_connection():
    """创建数据库连接"""
    config = get_config()
    
    try:
        # 首先连接到MySQL服务器（不指定数据库）
        connection = mysql.connector.connect(
            host=config.DB_HOST,
            port=config.DB_PORT,
            user=config.DB_USER,
            password=config.DB_PASSWORD
        )
        return connection
    except Error as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def execute_sql_file(connection, file_path):
    """执行SQL文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            sql_script = file.read()
        
        # 分割SQL语句
        sql_commands = sql_script.split(';')
        
        cursor = connection.cursor()
        
        for command in sql_commands:
            command = command.strip()
            if command:
                try:
                    cursor.execute(command)
                    connection.commit()
                except Error as e:
                    print(f"⚠️ 执行SQL命令时出错: {e}")
                    print(f"命令: {command[:100]}...")
        
        cursor.close()
        print(f"✅ 成功执行SQL文件: {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ 执行SQL文件失败: {e}")
        return False

def init_database():
    """初始化数据库"""
    print("🔧 开始初始化数据库...")
    
    # 创建数据库连接
    connection = create_database_connection()
    if not connection:
        return False
    
    try:
        # 获取脚本目录
        script_dir = Path(__file__).parent
        
        # 执行数据库初始化脚本
        init_sql_path = script_dir / "init_database.sql"
        if init_sql_path.exists():
            print("📋 执行数据库结构初始化...")
            if not execute_sql_file(connection, init_sql_path):
                return False
        else:
            print(f"❌ 找不到初始化脚本: {init_sql_path}")
            return False
        
        # 执行示例数据插入脚本
        sample_sql_path = script_dir / "sample_data.sql"
        if sample_sql_path.exists():
            print("📊 插入示例数据...")
            if not execute_sql_file(connection, sample_sql_path):
                print("⚠️ 示例数据插入失败，但数据库结构已创建")
        else:
            print(f"⚠️ 找不到示例数据脚本: {sample_sql_path}")
        
        print("✅ 数据库初始化完成！")
        return True
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False
    
    finally:
        if connection and connection.is_connected():
            connection.close()

def check_database_status():
    """检查数据库状态"""
    print("🔍 检查数据库状态...")
    
    config = get_config()
    
    try:
        connection = mysql.connector.connect(
            host=config.DB_HOST,
            port=config.DB_PORT,
            user=config.DB_USER,
            password=config.DB_PASSWORD,
            database=config.DB_NAME
        )
        
        cursor = connection.cursor()
        
        # 检查表是否存在
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        print(f"📋 数据库 '{config.DB_NAME}' 中的表:")
        for table in tables:
            print(f"  - {table[0]}")
        
        # 检查用户表数据
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        print(f"👥 用户数量: {user_count}")
        
        # 检查股票表数据
        cursor.execute("SELECT COUNT(*) FROM stocks")
        stock_count = cursor.fetchone()[0]
        print(f"📈 股票数量: {stock_count}")
        
        cursor.close()
        connection.close()
        
        print("✅ 数据库状态检查完成")
        return True
        
    except Error as e:
        print(f"❌ 数据库状态检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 股票分析系统数据库初始化工具")
    print("=" * 50)
    
    # 初始化数据库
    if init_database():
        print("\n" + "=" * 50)
        # 检查数据库状态
        check_database_status()
    else:
        print("❌ 数据库初始化失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
