"""
工具函数模块
Utility Functions Module

提供各种辅助功能和工具函数
"""

import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from typing import Dict, List, Any, Optional
from datetime import datetime, date, timedelta
import streamlit as st

def format_number(value: float, decimal_places: int = 2) -> str:
    """格式化数字显示"""
    if pd.isna(value):
        return "N/A"
    
    if abs(value) >= 1e8:
        return f"{value/1e8:.{decimal_places}f}亿"
    elif abs(value) >= 1e4:
        return f"{value/1e4:.{decimal_places}f}万"
    else:
        return f"{value:.{decimal_places}f}"

def format_percentage(value: float, decimal_places: int = 2) -> str:
    """格式化百分比显示"""
    if pd.isna(value):
        return "N/A"
    return f"{value:.{decimal_places}f}%"

def format_currency(value: float, currency: str = "¥") -> str:
    """格式化货币显示"""
    if pd.isna(value):
        return "N/A"
    return f"{currency}{format_number(value)}"

def calculate_period_return(prices: pd.Series, period: str = "1M") -> float:
    """计算指定期间的收益率"""
    if len(prices) < 2:
        return 0.0

    # 确保价格数据为float类型
    prices = pd.to_numeric(prices, errors='coerce').dropna()

    if len(prices) < 2:
        return 0.0

    period_map = {
        "1D": 1,
        "1W": 7,
        "1M": 30,
        "3M": 90,
        "6M": 180,
        "1Y": 252,
        "YTD": None  # 年初至今
    }

    if period == "YTD":
        # 计算年初至今收益率
        current_year = datetime.now().year
        year_start = datetime(current_year, 1, 1).date()

        # 找到年初最近的交易日
        year_start_price = None
        for i, (date_idx, price) in enumerate(prices.items()):
            if date_idx.date() >= year_start:
                year_start_price = float(price)
                break

        if year_start_price is not None and year_start_price > 0:
            return float((prices.iloc[-1] / year_start_price - 1) * 100)
        else:
            return 0.0

    days = period_map.get(period, 30)

    try:
        if len(prices) <= days:
            start_price = float(prices.iloc[0])
            end_price = float(prices.iloc[-1])
        else:
            start_price = float(prices.iloc[-days-1])
            end_price = float(prices.iloc[-1])

        if start_price > 0:
            return float((end_price / start_price - 1) * 100)
        else:
            return 0.0
    except (IndexError, ValueError, ZeroDivisionError):
        return 0.0

def create_candlestick_chart(df: pd.DataFrame, title: str = "股票价格走势") -> go.Figure:
    """创建K线图"""
    # 确保价格数据为数值类型
    price_columns = ['open_price', 'high_price', 'low_price', 'close_price']
    df_copy = df.copy()

    for col in price_columns:
        if col in df_copy.columns:
            df_copy[col] = pd.to_numeric(df_copy[col], errors='coerce')

    # 移除包含NaN的行
    df_copy = df_copy.dropna(subset=price_columns)

    if df_copy.empty:
        # 如果没有有效数据，创建空图表
        fig = go.Figure()
        fig.update_layout(
            title=title,
            xaxis_title="日期",
            yaxis_title="价格 (元)",
            annotations=[{
                'text': '暂无有效数据',
                'x': 0.5,
                'y': 0.5,
                'xref': 'paper',
                'yref': 'paper',
                'showarrow': False,
                'font': {'size': 16}
            }]
        )
        return fig

    fig = go.Figure(data=go.Candlestick(
        x=df_copy.index,
        open=df_copy['open_price'],
        high=df_copy['high_price'],
        low=df_copy['low_price'],
        close=df_copy['close_price'],
        name="K线"
    ))

    fig.update_layout(
        title=title,
        xaxis_title="日期",
        yaxis_title="价格 (元)",
        xaxis_rangeslider_visible=False,
        height=500
    )

    return fig

def create_line_chart(df: pd.DataFrame, y_column: str, title: str = "价格走势") -> go.Figure:
    """创建折线图"""
    fig = go.Figure()

    # 确保数据为数值类型
    df_copy = df.copy()
    if y_column in df_copy.columns:
        df_copy[y_column] = pd.to_numeric(df_copy[y_column], errors='coerce')
        df_copy = df_copy.dropna(subset=[y_column])

    if df_copy.empty or y_column not in df_copy.columns:
        # 如果没有有效数据，创建空图表
        fig.update_layout(
            title=title,
            xaxis_title="日期",
            yaxis_title="价格 (元)",
            height=400,
            annotations=[{
                'text': '暂无有效数据',
                'x': 0.5,
                'y': 0.5,
                'xref': 'paper',
                'yref': 'paper',
                'showarrow': False,
                'font': {'size': 16}
            }]
        )
        return fig

    fig.add_trace(go.Scatter(
        x=df_copy.index,
        y=df_copy[y_column],
        mode='lines',
        name=title,
        line=dict(width=2)
    ))

    fig.update_layout(
        title=title,
        xaxis_title="日期",
        yaxis_title="价格 (元)",
        height=400
    )

    return fig

def create_portfolio_pie_chart(holdings: Dict[str, float], title: str = "投资组合分布") -> go.Figure:
    """创建投资组合饼图"""
    labels = list(holdings.keys())
    values = list(holdings.values())
    
    fig = go.Figure(data=go.Pie(
        labels=labels,
        values=values,
        hole=0.3
    ))
    
    fig.update_layout(
        title=title,
        height=400
    )
    
    return fig

def create_returns_comparison_chart(returns_data: Dict[str, List[float]], 
                                  labels: List[str], 
                                  title: str = "收益率对比") -> go.Figure:
    """创建收益率对比图"""
    fig = go.Figure()
    
    for stock_code, returns in returns_data.items():
        fig.add_trace(go.Bar(
            name=stock_code,
            x=labels,
            y=returns
        ))
    
    fig.update_layout(
        title=title,
        xaxis_title="时间周期",
        yaxis_title="收益率 (%)",
        barmode='group',
        height=400
    )
    
    return fig

def display_metric_cards(metrics: Dict[str, Any], columns: int = 4):
    """显示指标卡片"""
    cols = st.columns(columns)
    
    for i, (key, value) in enumerate(metrics.items()):
        with cols[i % columns]:
            if isinstance(value, dict):
                st.metric(
                    label=value.get('label', key),
                    value=value.get('value', 'N/A'),
                    delta=value.get('delta', None)
                )
            else:
                st.metric(label=key, value=value)

def get_color_for_value(value: float, positive_color: str = "green", 
                       negative_color: str = "red", neutral_color: str = "gray") -> str:
    """根据数值获取颜色"""
    if value > 0:
        return positive_color
    elif value < 0:
        return negative_color
    else:
        return neutral_color

def validate_date_range(start_date: date, end_date: date) -> bool:
    """验证日期范围"""
    if start_date >= end_date:
        st.error("❌ 开始日期必须早于结束日期")
        return False

    if end_date > date.today():
        st.error("❌ 结束日期不能超过今天")
        return False

    return True

def get_trading_days(start_date: date, end_date: date) -> int:
    """计算交易日天数（简化版本，不考虑节假日）"""
    total_days = (end_date - start_date).days
    # 简化计算：假设每周5个交易日
    return int(total_days * 5 / 7)

def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """安全除法，避免除零错误"""
    if denominator == 0 or pd.isna(denominator) or pd.isna(numerator):
        return default
    return numerator / denominator
