"""
应用配置文件
Application Configuration

管理应用的各种配置参数
"""

import os
from typing import Dict, Any

class Config:
    """应用配置类"""
    
    # 数据库配置
    DB_HOST = os.getenv('DB_HOST', 'localhost')
    DB_PORT = int(os.getenv('DB_PORT', 3306))
    DB_USER = os.getenv('DB_USER', 'stock_user')
    DB_PASSWORD = os.getenv('DB_PASSWORD', 'stock_pass')
    DB_NAME = os.getenv('DB_NAME', 'stock_analysis')
    
    # Streamlit 配置
    APP_TITLE = "股票分析系统"
    APP_ICON = "📈"
    PAGE_LAYOUT = "wide"
    
    # 应用设置
    DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'
    SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-here')
    
    # 股票分析配置
    DEFAULT_ANALYSIS_PERIOD = 365  # 默认分析周期（天）
    MAX_PORTFOLIO_STOCKS = 20      # 投资组合最大股票数量
    MIN_PORTFOLIO_STOCKS = 2       # 投资组合最小股票数量
    
    # 风险评估参数
    RISK_FREE_RATE = 0.03          # 无风险利率
    LOW_VOLATILITY_THRESHOLD = 15   # 低波动率阈值
    HIGH_VOLATILITY_THRESHOLD = 25  # 高波动率阈值
    
    @classmethod
    def get_db_config(cls) -> Dict[str, Any]:
        """获取数据库配置"""
        return {
            'host': cls.DB_HOST,
            'port': cls.DB_PORT,
            'user': cls.DB_USER,
            'password': cls.DB_PASSWORD,
            'database': cls.DB_NAME,
            'charset': 'utf8mb4',
            'autocommit': True
        }
    
    @classmethod
    def get_streamlit_config(cls) -> Dict[str, Any]:
        """获取Streamlit配置"""
        return {
            'page_title': cls.APP_TITLE,
            'page_icon': cls.APP_ICON,
            'layout': cls.PAGE_LAYOUT,
            'initial_sidebar_state': 'expanded'
        }

# 开发环境配置
class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    DB_HOST = 'localhost'

# 生产环境配置
class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False

# 测试环境配置
class TestingConfig(Config):
    """测试环境配置"""
    DEBUG = True
    DB_NAME = 'stock_analysis_test'

# 根据环境变量选择配置
config_map = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig
}

def get_config():
    """获取当前环境配置"""
    env = os.getenv('FLASK_ENV', 'development')
    return config_map.get(env, DevelopmentConfig)
