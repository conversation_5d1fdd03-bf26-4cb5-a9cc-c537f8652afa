# 📈 股票分析系统 (Stock Analysis System)

一个基于 Streamlit 和 MySQL 的专业股票数据分析平台，提供股票数据展示、技术分析和投资组合管理功能。

## 🌟 功能特性

### 核心功能

- **用户认证系统**: 安全的用户登录和注册功能
- **股票数据展示**: 实时股票价格和历史数据查看
- **技术分析**: 多种技术指标计算和图表展示
- **投资组合分析**: 创建和管理个人投资组合
- **风险评估**: 收益率、波动率、夏普比率等风险指标

### 数据分析功能

- 📊 **单只股票分析**
  - 价格走势图（K 线图、折线图）
  - 收益率分析（日、月、年化收益率）
  - 风险指标（波动率、最大回撤、夏普比率）
- 💼 **投资组合分析**

  - 组合构建和权重分配
  - 组合风险收益指标
  - 行业分散化分析
  - 历史表现回测

- 📈 **市场概览**
  - 热门股票排行
  - 行业分布统计
  - 市场整体表现

## 🛠️ 技术栈

- **后端**: Python 3.11+
- **前端**: Streamlit
- **数据库**: MySQL 8.0
- **数据处理**: Pandas, NumPy
- **数据可视化**: Plotly
- **容器化**: Docker, Docker Compose
- **密码加密**: bcrypt

## 📋 系统要求

- Python 3.11 或更高版本
- MySQL 8.0 或更高版本
- Docker 和 Docker Compose（可选）
- 至少 4GB RAM
- 至少 2GB 可用磁盘空间

## 🚀 快速开始

### 方法一：Docker 部署（推荐）

1. **克隆项目**

```bash
git clone <repository-url>
cd stock-analysis-app
```

2. **启动服务**

```bash
docker-compose up -d
```

3. **访问应用**

- 打开浏览器访问: http://localhost:8501
- 使用演示账户登录:
  - 用户名: `admin`
  - 密码: `admin123`

### 方法二：本地开发部署

1. **环境准备**

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

2. **数据库配置**

```bash
# 启动 MySQL 服务
# 创建数据库（使用root用户）
mysql -u root -p123456
CREATE DATABASE stock_analysis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

3. **初始化数据库**

```bash
python scripts/init_db.py
```

4. **启动应用**

```bash
python scripts/start.py
```

## 📁 项目结构

```
stock-analysis-app/
├── app/                    # 应用主目录
│   ├── auth/              # 认证模块
│   ├── database/          # 数据库连接
│   ├── models/            # 数据模型
│   ├── pages/             # 页面组件
│   ├── utils/             # 工具函数
│   └── main.py            # 主应用入口
├── config/                # 配置文件
├── scripts/               # 脚本文件
│   ├── init_database.sql  # 数据库初始化
│   ├── sample_data.sql    # 示例数据
│   ├── init_db.py         # 数据库初始化脚本
│   └── start.py           # 应用启动脚本
├── tests/                 # 测试文件
├── docs/                  # 文档
├── data/                  # 数据文件
├── docker-compose.yml     # Docker 编排文件
├── Dockerfile             # Docker 镜像文件
├── requirements.txt       # Python 依赖
└── README.md              # 项目说明
```

## 🔧 配置说明

### 环境变量配置

复制 `.env.example` 为 `.env` 并修改配置：

```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=123456
DB_NAME=stock_analysis

# 应用配置
DEBUG=False
SECRET_KEY=your-secret-key-here
```

### 数据库配置

系统使用 MySQL 8.0 作为数据库，主要包含以下表：

- `users`: 用户信息表
- `stocks`: 股票基础信息表
- `stock_data`: 股票历史数据表
- `user_portfolios`: 用户投资组合表
- `portfolio_holdings`: 投资组合持仓表

## 📊 使用指南

### 1. 用户登录

- 访问系统首页
- 使用演示账户或注册新账户
- 登录后进入主界面

### 2. 股票分析

- 在"股票分析"页面选择要分析的股票
- 查看价格走势图和技术指标
- 分析收益率和风险指标

### 3. 投资组合管理

- 在"投资组合分析"页面创建新组合
- 添加股票并设置权重
- 查看组合风险收益指标

## 🧪 测试

运行测试套件：

```bash
# 运行所有测试
python -m pytest tests/

# 运行特定测试
python -m pytest tests/test_models.py

# 生成测试覆盖率报告
python -m pytest --cov=app tests/
```

## 📝 开发指南

### 添加新功能

1. 在 `app/models/` 中添加数据模型
2. 在 `app/pages/` 中创建页面组件
3. 在 `app/utils/` 中添加工具函数
4. 更新 `app/main.py` 中的路由

### 代码规范

- 遵循 PEP 8 代码规范
- 使用类型提示
- 编写详细的文档字符串
- 添加适当的错误处理

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请：

1. 查看 [文档](docs/)
2. 提交 [Issue](issues)
3. 联系开发团队

## 🔄 更新日志

### v1.0.0 (2024-01-01)

- 初始版本发布
- 基础股票分析功能
- 用户认证系统
- 投资组合管理
- Docker 容器化支持
