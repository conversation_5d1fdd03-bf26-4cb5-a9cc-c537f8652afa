"""
项目清理脚本
Project Cleanup Script

清理不必要的文件和目录，保持项目整洁
"""

import os
import shutil
from pathlib import Path

def cleanup_cache_files():
    """清理缓存文件"""
    print("🧹 清理Python缓存文件...")
    
    project_root = Path(__file__).parent.parent
    
    # 查找并删除__pycache__目录
    pycache_dirs = list(project_root.rglob("__pycache__"))
    for cache_dir in pycache_dirs:
        try:
            shutil.rmtree(cache_dir)
            print(f"✅ 删除: {cache_dir}")
        except Exception as e:
            print(f"❌ 删除失败 {cache_dir}: {e}")
    
    # 查找并删除.pyc文件
    pyc_files = list(project_root.rglob("*.pyc"))
    for pyc_file in pyc_files:
        try:
            pyc_file.unlink()
            print(f"✅ 删除: {pyc_file}")
        except Exception as e:
            print(f"❌ 删除失败 {pyc_file}: {e}")

def cleanup_empty_directories():
    """清理空目录"""
    print("\n🧹 清理空目录...")
    
    project_root = Path(__file__).parent.parent
    
    # 检查并删除空目录
    empty_dirs = []
    for root, dirs, files in os.walk(project_root):
        for dir_name in dirs:
            dir_path = Path(root) / dir_name
            try:
                if not any(dir_path.iterdir()):  # 目录为空
                    empty_dirs.append(dir_path)
            except:
                pass
    
    for empty_dir in empty_dirs:
        try:
            empty_dir.rmdir()
            print(f"✅ 删除空目录: {empty_dir}")
        except Exception as e:
            print(f"❌ 删除失败 {empty_dir}: {e}")

def cleanup_unnecessary_files():
    """清理不必要的文件"""
    print("\n🧹 清理不必要的文件...")
    
    project_root = Path(__file__).parent.parent
    
    # 要删除的文件模式
    patterns_to_delete = [
        "*.log",
        "*.tmp",
        "*.temp",
        ".DS_Store",
        "Thumbs.db",
        "*.bak",
        "*.swp",
        "*.swo"
    ]
    
    for pattern in patterns_to_delete:
        files = list(project_root.rglob(pattern))
        for file_path in files:
            try:
                file_path.unlink()
                print(f"✅ 删除: {file_path}")
            except Exception as e:
                print(f"❌ 删除失败 {file_path}: {e}")

def organize_project_structure():
    """整理项目结构"""
    print("\n📁 检查项目结构...")
    
    project_root = Path(__file__).parent.parent
    
    # 期望的项目结构
    expected_structure = {
        "app": {
            "auth": ["login.py"],
            "database": ["connection.py"],
            "models": ["stock.py", "user.py"],
            "utils": ["helpers.py"],
            "views": ["dashboard.py", "stock_analysis.py", "portfolio_analysis.py"],
            "files": ["main.py"]
        },
        "scripts": ["cleanup_project.py", "test_data_types.py", "test_ui_fixes.py", "verify_all_fixes.py"],
        "files": ["start_app.py", "README.md", "requirements.txt"]
    }
    
    def check_structure(base_path, structure, level=0):
        indent = "  " * level
        for key, value in structure.items():
            if key == "files":
                # 检查文件
                for file_name in value:
                    file_path = base_path / file_name
                    if file_path.exists():
                        print(f"{indent}✅ {file_name}")
                    else:
                        print(f"{indent}❌ {file_name} (缺失)")
            else:
                # 检查目录
                dir_path = base_path / key
                if dir_path.exists():
                    print(f"{indent}📁 {key}/")
                    if isinstance(value, dict):
                        check_structure(dir_path, value, level + 1)
                    elif isinstance(value, list):
                        for file_name in value:
                            file_path = dir_path / file_name
                            if file_path.exists():
                                print(f"{indent}  ✅ {file_name}")
                            else:
                                print(f"{indent}  ❌ {file_name} (缺失)")
                else:
                    print(f"{indent}❌ {key}/ (目录缺失)")
    
    print("当前项目结构:")
    check_structure(project_root, expected_structure)

def show_project_size():
    """显示项目大小"""
    print("\n📊 项目大小统计...")
    
    project_root = Path(__file__).parent.parent
    
    total_size = 0
    file_count = 0
    dir_count = 0
    
    for root, dirs, files in os.walk(project_root):
        dir_count += len(dirs)
        for file in files:
            file_path = Path(root) / file
            try:
                size = file_path.stat().st_size
                total_size += size
                file_count += 1
            except:
                pass
    
    # 转换大小单位
    if total_size < 1024:
        size_str = f"{total_size} B"
    elif total_size < 1024 * 1024:
        size_str = f"{total_size / 1024:.1f} KB"
    else:
        size_str = f"{total_size / (1024 * 1024):.1f} MB"
    
    print(f"📁 目录数量: {dir_count}")
    print(f"📄 文件数量: {file_count}")
    print(f"💾 总大小: {size_str}")

def main():
    """主函数"""
    print("🧹 项目清理工具")
    print("=" * 50)
    
    try:
        # 执行清理操作
        cleanup_cache_files()
        cleanup_empty_directories()
        cleanup_unnecessary_files()
        
        print("\n" + "=" * 50)
        
        # 显示项目结构和统计
        organize_project_structure()
        show_project_size()
        
        print("\n🎉 项目清理完成！")
        print("\n📋 清理总结:")
        print("✅ 删除了Python缓存文件")
        print("✅ 删除了空目录")
        print("✅ 删除了临时文件")
        print("✅ 检查了项目结构")
        
        print("\n💡 建议:")
        print("- 定期运行此脚本保持项目整洁")
        print("- 使用 start_app.py 启动应用")
        print("- 查看 README.md 了解更多信息")
        
    except Exception as e:
        print(f"\n❌ 清理过程出错: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    if not success:
        exit(1)
