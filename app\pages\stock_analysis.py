"""
股票分析页面
Stock Analysis Page

提供单只股票的详细分析功能
"""

import streamlit as st
import pandas as pd
from datetime import datetime, date, timedelta
from app.models.stock import StockManager
from app.utils.helpers import (
    format_number, format_percentage, format_currency,
    display_metric_cards, create_candlestick_chart, create_line_chart,
    calculate_period_return, validate_date_range
)

def stock_analysis_page():
    """股票分析主页面"""
    
    st.title("📈 股票分析")
    st.markdown("---")
    
    # 初始化股票管理器
    stock_manager = StockManager()
    
    # 获取所有股票列表
    stocks = stock_manager.get_all_stocks()
    
    if not stocks:
        st.error("暂无股票数据，请联系管理员")
        return
    
    # 股票选择器
    stock_options = [f"{stock.stock_code} - {stock.stock_name}" for stock in stocks]
    selected_stock = st.selectbox(
        "选择要分析的股票",
        options=stock_options,
        index=0
    )
    
    if selected_stock:
        stock_code = selected_stock.split(' - ')[0]
        stock_name = selected_stock.split(' - ')[1]
        
        # 显示股票基本信息
        show_stock_info(stock_manager, stock_code, stock_name)
        
        st.markdown("---")
        
        # 显示价格走势分析
        show_price_analysis(stock_manager, stock_code, stock_name)
        
        st.markdown("---")
        
        # 显示收益率分析
        show_returns_analysis(stock_manager, stock_code, stock_name)

def show_stock_info(stock_manager: StockManager, stock_code: str, stock_name: str):
    """显示股票基本信息"""
    
    st.subheader(f"📋 {stock_name} ({stock_code}) 基本信息")
    
    # 获取股票详细信息
    stock = stock_manager.get_stock_by_code(stock_code)
    latest_price = stock_manager.get_latest_price(stock_code)
    
    if stock:
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("股票代码", stock_code)
        
        with col2:
            st.metric("所属市场", stock.market)
        
        with col3:
            st.metric("所属行业", stock.industry or "N/A")
        
        with col4:
            st.metric("最新价格", format_currency(latest_price) if latest_price else "N/A")

def show_price_analysis(stock_manager: StockManager, stock_code: str, stock_name: str):
    """显示价格走势分析"""
    
    st.subheader(f"📊 {stock_name} 价格走势分析")
    
    # 时间范围选择
    col1, col2 = st.columns(2)
    
    with col1:
        period = st.selectbox(
            "选择分析周期",
            options=["1个月", "3个月", "6个月", "1年", "自定义"],
            index=2
        )
    
    with col2:
        chart_type = st.selectbox(
            "图表类型",
            options=["K线图", "折线图"],
            index=0
        )
    
    # 根据选择的周期计算日期范围
    end_date = date.today()
    
    if period == "1个月":
        start_date = end_date - timedelta(days=30)
    elif period == "3个月":
        start_date = end_date - timedelta(days=90)
    elif period == "6个月":
        start_date = end_date - timedelta(days=180)
    elif period == "1年":
        start_date = end_date - timedelta(days=365)
    else:  # 自定义
        col1, col2 = st.columns(2)
        with col1:
            start_date = st.date_input("开始日期", value=end_date - timedelta(days=90))
        with col2:
            end_date = st.date_input("结束日期", value=end_date)
        
        if not validate_date_range(start_date, end_date):
            return
    
    # 获取股票数据
    df = stock_manager.get_stock_data(stock_code, start_date, end_date)
    
    if df.empty:
        st.warning(f"在选定时间范围内没有找到 {stock_name} 的数据")
        return
    
    # 显示图表
    if chart_type == "K线图":
        fig = create_candlestick_chart(df, f"{stock_name} K线图")
    else:
        fig = create_line_chart(df, 'close_price', f"{stock_name} 价格走势")
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 显示价格统计信息
    show_price_statistics(df, stock_name)

def show_price_statistics(df: pd.DataFrame, stock_name: str):
    """显示价格统计信息"""
    
    st.subheader(f"📊 {stock_name} 价格统计")
    
    if df.empty:
        return
    
    # 计算统计指标
    current_price = df['close_price'].iloc[-1]
    period_high = df['high_price'].max()
    period_low = df['low_price'].min()
    avg_price = df['close_price'].mean()
    avg_volume = df['volume'].mean()
    
    # 计算价格变化
    price_change = df['close_price'].iloc[-1] - df['close_price'].iloc[0]
    price_change_pct = (price_change / df['close_price'].iloc[0]) * 100
    
    # 显示指标卡片
    col1, col2, col3, col4, col5 = st.columns(5)
    
    with col1:
        st.metric("当前价格", format_currency(current_price))
    
    with col2:
        st.metric("期间最高", format_currency(period_high))
    
    with col3:
        st.metric("期间最低", format_currency(period_low))
    
    with col4:
        st.metric("平均价格", format_currency(avg_price))
    
    with col5:
        st.metric("平均成交量", format_number(avg_volume))
    
    # 显示价格变化
    col1, col2 = st.columns(2)
    
    with col1:
        st.metric(
            "期间涨跌",
            format_currency(price_change),
            delta=format_percentage(price_change_pct)
        )
    
    with col2:
        # 计算距离最高点的回撤
        drawdown = (current_price - period_high) / period_high * 100
        st.metric(
            "距最高点",
            format_percentage(drawdown),
            delta=format_percentage(drawdown)
        )

def show_returns_analysis(stock_manager: StockManager, stock_code: str, stock_name: str):
    """显示收益率分析"""
    
    st.subheader(f"📈 {stock_name} 收益率分析")
    
    # 获取不同周期的收益率数据
    periods = [7, 30, 90, 180, 365]
    period_names = ["1周", "1个月", "3个月", "6个月", "1年"]
    
    returns_data = []
    
    for period in periods:
        returns = stock_manager.calculate_returns(stock_code, period)
        returns_data.append(returns)
    
    # 创建收益率对比表格
    returns_df = pd.DataFrame({
        '时间周期': period_names,
        '总收益率': [f"{data.get('total_return', 0):.2f}%" for data in returns_data],
        '年化收益率': [f"{data.get('annual_return', 0):.2f}%" for data in returns_data],
        '波动率': [f"{data.get('volatility', 0):.2f}%" for data in returns_data],
        '夏普比率': [f"{data.get('sharpe_ratio', 0):.2f}" for data in returns_data],
        '最大回撤': [f"{data.get('max_drawdown', 0):.2f}%" for data in returns_data]
    })
    
    st.dataframe(returns_df, use_container_width=True, hide_index=True)
    
    # 显示风险收益分析
    st.subheader("⚖️ 风险收益分析")
    
    # 使用1年期数据进行详细分析
    annual_returns = returns_data[-1] if returns_data else {}
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric(
            "年化收益率",
            format_percentage(annual_returns.get('annual_return', 0)),
            help="过去一年的年化收益率"
        )
    
    with col2:
        st.metric(
            "年化波动率",
            format_percentage(annual_returns.get('volatility', 0)),
            help="价格波动的标准差，反映投资风险"
        )
    
    with col3:
        sharpe = annual_returns.get('sharpe_ratio', 0)
        st.metric(
            "夏普比率",
            f"{sharpe:.2f}",
            help="风险调整后的收益率，数值越高越好"
        )
    
    # 风险等级评估
    st.subheader("🎯 风险等级评估")
    
    volatility = annual_returns.get('volatility', 0)
    
    if volatility < 15:
        risk_level = "低风险"
        risk_color = "green"
    elif volatility < 25:
        risk_level = "中等风险"
        risk_color = "orange"
    else:
        risk_level = "高风险"
        risk_color = "red"
    
    st.markdown(f"**风险等级**: :{risk_color}[{risk_level}]")
    st.markdown(f"**波动率**: {format_percentage(volatility)}")
    
    if sharpe > 1:
        st.success("💡 该股票具有较好的风险调整收益率")
    elif sharpe > 0.5:
        st.info("💡 该股票具有中等的风险调整收益率")
    else:
        st.warning("💡 该股票的风险调整收益率较低，请谨慎投资")
