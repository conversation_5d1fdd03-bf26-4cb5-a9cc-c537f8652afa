"""
完整功能测试脚本
Complete Function Test Script

测试股票分析应用的所有主要功能
"""

import sys
import os
from pathlib import Path
import time

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

def test_database_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    
    try:
        from app.database.connection import DatabaseManager
        
        db_manager = DatabaseManager()
        if db_manager.connect():
            print("✅ 数据库连接成功")
            
            # 测试查询
            result = db_manager.execute_query("SELECT COUNT(*) as count FROM users")
            if result:
                print(f"✅ 用户表查询成功，用户数量: {result[0]['count']}")
            
            db_manager.disconnect()
            return True
        else:
            print("❌ 数据库连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        return False

def test_user_authentication():
    """测试用户认证"""
    print("\n🔐 测试用户认证...")
    
    try:
        from app.models.user import UserManager
        
        user_manager = UserManager()
        
        # 测试admin用户认证
        print("测试admin用户登录...")
        user = user_manager.authenticate_user("admin", "admin123")
        
        if user:
            print("✅ admin用户认证成功")
            print(f"   用户ID: {user.id}")
            print(f"   用户名: {user.username}")
            print(f"   邮箱: {user.email}")
            return True
        else:
            print("❌ admin用户认证失败")
            return False
            
    except Exception as e:
        print(f"❌ 用户认证测试失败: {e}")
        return False

def test_stock_data():
    """测试股票数据"""
    print("\n📈 测试股票数据...")
    
    try:
        from app.models.stock import StockManager
        
        stock_manager = StockManager()
        
        # 获取所有股票
        stocks = stock_manager.get_all_stocks()
        print(f"✅ 获取到 {len(stocks)} 只股票")
        
        if len(stocks) > 0:
            # 测试获取股票数据
            first_stock = stocks[0]
            print(f"测试股票: {first_stock.stock_code} - {first_stock.stock_name}")
            
            # 获取股票历史数据
            stock_data = stock_manager.get_stock_data(first_stock.stock_code)
            print(f"✅ 获取到 {len(stock_data)} 条历史数据")
            
            # 获取最新价格
            latest_price = stock_manager.get_latest_price(first_stock.stock_code)
            if latest_price:
                print(f"✅ 最新价格: ¥{latest_price}")
            
            # 计算收益率指标
            returns = stock_manager.calculate_returns(first_stock.stock_code, 30)
            if returns:
                print(f"✅ 30日收益率: {returns.get('total_return', 0):.2f}%")
            
            return True
        else:
            print("❌ 没有找到股票数据")
            return False
            
    except Exception as e:
        print(f"❌ 股票数据测试失败: {e}")
        return False

def test_streamlit_config():
    """测试Streamlit配置"""
    print("\n⚙️ 测试Streamlit配置...")
    
    config_path = ".streamlit/config.toml"
    
    if os.path.exists(config_path):
        print("✅ Streamlit配置文件存在")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查关键配置
            checks = [
                ('address = "localhost"', "服务器地址配置"),
                ('port = 8501', "端口配置"),
                ('fileWatcherType = "poll"', "文件监控配置"),
                ('enableCORS = false', "CORS配置"),
                ('enableXsrfProtection = false', "XSRF保护配置")
            ]
            
            all_good = True
            for check, desc in checks:
                if check in content:
                    print(f"✅ {desc}")
                else:
                    print(f"❌ {desc} - 缺失: {check}")
                    all_good = False
            
            return all_good
            
        except Exception as e:
            print(f"❌ 读取配置文件失败: {e}")
            return False
    else:
        print("❌ Streamlit配置文件不存在")
        return False

def test_import_modules():
    """测试模块导入"""
    print("\n📦 测试模块导入...")
    
    modules = [
        ("streamlit", "Streamlit框架"),
        ("pandas", "数据处理"),
        ("numpy", "数值计算"),
        ("plotly", "图表库"),
        ("bcrypt", "密码加密"),
        ("mysql.connector", "MySQL连接器")
    ]
    
    all_imported = True
    
    for module_name, desc in modules:
        try:
            if module_name == "mysql.connector":
                import mysql.connector
            else:
                __import__(module_name)
            print(f"✅ {desc} ({module_name})")
        except ImportError:
            print(f"❌ {desc} ({module_name}) - 未安装")
            all_imported = False
    
    return all_imported

def show_startup_guide():
    """显示启动指南"""
    print("\n🚀 应用启动指南:")
    print("=" * 50)
    print("1. 确保conda环境已激活:")
    print("   conda activate stock")
    print()
    print("2. 启动应用:")
    print("   streamlit run app/main.py --server.port=8501 --server.address=localhost")
    print()
    print("3. 访问应用:")
    print("   http://localhost:8501")
    print()
    print("4. 登录信息:")
    print("   用户名: admin")
    print("   密码: admin123")
    print()
    print("5. 如果遇到问题:")
    print("   - 运行修复脚本: python scripts/fix_login.py")
    print("   - 运行诊断脚本: python scripts/diagnose_login.py")

def main():
    """主函数"""
    print("🧪 股票分析应用完整功能测试")
    print("=" * 60)
    
    # 执行所有测试
    tests = [
        ("模块导入", test_import_modules),
        ("数据库连接", test_database_connection),
        ("用户认证", test_user_authentication),
        ("股票数据", test_stock_data),
        ("Streamlit配置", test_streamlit_config)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试出错: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n📊 测试结果汇总:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！应用已准备就绪")
        show_startup_guide()
        return True
    else:
        print(f"\n⚠️ {total - passed} 项测试失败，请检查上述错误")
        print("\n🔧 建议运行修复脚本:")
        print("   python scripts/fix_login.py")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
