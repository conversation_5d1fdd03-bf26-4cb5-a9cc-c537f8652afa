# 股票分析应用 Docker Compose 配置
# Stock Analysis Application Docker Compose Configuration

version: "3.8"

services:
  # MySQL 数据库服务
  mysql:
    image: mysql:8.0
    container_name: stock_analysis_mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: stock_analysis
      MYSQL_USER: stock_user
      MYSQL_PASSWORD: stock_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init_database.sql:/docker-entrypoint-initdb.d/01-init.sql
      - ./scripts/sample_data.sql:/docker-entrypoint-initdb.d/02-sample.sql
    networks:
      - stock_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Streamlit 应用服务
  app:
    build: .
    container_name: stock_analysis_app
    restart: always
    ports:
      - "8501:8501"
    environment:
      DB_HOST: mysql
      DB_PORT: 3306
      DB_USER: stock_user
      DB_PASSWORD: stock_pass
      DB_NAME: stock_analysis
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - stock_network
    volumes:
      - ./app:/app/app
      - ./data:/app/data

volumes:
  mysql_data:

networks:
  stock_network:
    driver: bridge
