"""
仪表板页面
Dashboard Page

显示股票市场概览和用户投资组合摘要
"""

import streamlit as st
import pandas as pd
from datetime import datetime, date, timedelta
from app.models.stock import StockManager
from app.utils.helpers import (
    format_number, format_percentage, format_currency,
    display_metric_cards, create_line_chart, get_color_for_value
)

def dashboard_page():
    """仪表板主页面"""
    
    st.title("📊 投资仪表板")
    st.markdown("---")
    
    # 初始化股票管理器
    stock_manager = StockManager()
    
    # 显示市场概览
    show_market_overview(stock_manager)
    
    st.markdown("---")
    
    # 显示热门股票
    show_popular_stocks(stock_manager)
    
    st.markdown("---")
    
    # 显示用户投资组合摘要（如果有的话）
    show_portfolio_summary()

def show_market_overview(stock_manager: StockManager):
    """显示市场概览"""
    
    st.subheader("🏢 市场概览")
    
    # 获取所有股票
    stocks = stock_manager.get_all_stocks()
    
    if not stocks:
        st.warning("暂无股票数据")
        return
    
    # 计算市场统计数据
    total_stocks = len(stocks)
    
    # 按行业分组统计
    industry_counts = {}
    for stock in stocks:
        industry = stock.industry or "其他"
        industry_counts[industry] = industry_counts.get(industry, 0) + 1
    
    # 显示统计指标
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("股票总数", total_stocks)
    
    with col2:
        st.metric("行业数量", len(industry_counts))
    
    with col3:
        # 获取最新交易日
        latest_date = datetime.now().strftime("%Y-%m-%d")
        st.metric("最新交易日", latest_date)
    
    with col4:
        st.metric("活跃股票", total_stocks)
    
    # 显示行业分布
    if industry_counts:
        st.subheader("📈 行业分布")
        
        # 创建行业分布图表
        industry_df = pd.DataFrame(
            list(industry_counts.items()), 
            columns=['行业', '股票数量']
        ).sort_values('股票数量', ascending=False)
        
        st.bar_chart(industry_df.set_index('行业'))

def show_popular_stocks(stock_manager: StockManager):
    """显示热门股票"""
    
    st.subheader("🔥 热门股票")
    
    # 获取所有股票
    stocks = stock_manager.get_all_stocks()
    
    if not stocks:
        st.warning("暂无股票数据")
        return
    
    # 创建股票表格数据
    stock_data = []
    
    for stock in stocks[:10]:  # 只显示前10只股票
        # 获取最新价格
        latest_price = stock_manager.get_latest_price(stock.stock_code)
        
        # 计算收益率指标
        returns = stock_manager.calculate_returns(stock.stock_code, 30)
        
        stock_data.append({
            '股票代码': stock.stock_code,
            '股票名称': stock.stock_name,
            '所属行业': stock.industry or "N/A",
            '最新价格': format_currency(latest_price) if latest_price else "N/A",
            '30日收益率': format_percentage(returns.get('total_return', 0)),
            '年化收益率': format_percentage(returns.get('annual_return', 0)),
            '波动率': format_percentage(returns.get('volatility', 0)),
            '夏普比率': f"{returns.get('sharpe_ratio', 0):.2f}",
            '最大回撤': format_percentage(returns.get('max_drawdown', 0))
        })
    
    if stock_data:
        df = pd.DataFrame(stock_data)
        
        # 使用可交互的数据表格
        st.dataframe(
            df,
            use_container_width=True,
            hide_index=True
        )
        
        # 添加股票选择器用于查看详细信息
        st.subheader("📋 股票详细信息")

        selected_stock = st.selectbox(
            "🔍 选择股票查看详细走势图",
            options=[f"{stock['股票代码']} - {stock['股票名称']}" for stock in stock_data],
            index=0
        )
        
        if selected_stock:
            stock_code = selected_stock.split(' - ')[0]
            show_stock_chart(stock_manager, stock_code)

def show_stock_chart(stock_manager: StockManager, stock_code: str):
    """显示股票价格走势图"""
    
    # 获取股票数据
    end_date = date.today()
    start_date = end_date - timedelta(days=90)  # 显示最近3个月数据
    
    df = stock_manager.get_stock_data(stock_code, start_date, end_date)
    
    if not df.empty:
        # 创建价格走势图
        fig = create_line_chart(df, 'close_price', f"{stock_code} 价格走势（最近3个月）")
        st.plotly_chart(fig, use_container_width=True)
        
        # 显示最近5天的数据
        st.subheader("📅 最近交易数据")
        recent_data = df.tail(5)[['open_price', 'high_price', 'low_price', 'close_price', 'volume']]
        recent_data.columns = ['开盘价', '最高价', '最低价', '收盘价', '成交量']
        
        # 格式化显示
        for col in ['开盘价', '最高价', '最低价', '收盘价']:
            recent_data[col] = recent_data[col].apply(lambda x: f"¥{x:.2f}")
        recent_data['成交量'] = recent_data['成交量'].apply(format_number)
        
        st.dataframe(recent_data, use_container_width=True)
    else:
        st.warning(f"暂无 {stock_code} 的历史数据")

def show_portfolio_summary():
    """显示投资组合摘要"""
    
    st.subheader("💼 我的投资组合")
    
    # 这里可以根据用户ID获取实际的投资组合数据
    # 目前显示示例数据
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.info("💡 **提示**: 在「投资组合分析」页面可以创建和管理您的投资组合")
    
    with col2:
        if st.button("💼 创建投资组合", use_container_width=True):
            st.switch_page("app/pages/portfolio_analysis.py")
    
    # 显示示例投资组合数据
    st.markdown("#### 📊 示例投资组合表现")
    
    sample_metrics = {
        "总资产": {"value": "¥100,000", "delta": "+5.2%"},
        "今日收益": {"value": "¥1,250", "delta": "+1.25%"},
        "总收益率": {"value": "15.8%", "delta": "+2.1%"},
        "持仓股票": {"value": "5只", "delta": None}
    }
    
    display_metric_cards(sample_metrics, 4)
