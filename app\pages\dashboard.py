"""
仪表板页面
Dashboard Page

显示股票市场概览和用户投资组合摘要
"""

import streamlit as st
import pandas as pd
from datetime import datetime, date, timedelta
from app.models.stock import StockManager
from app.utils.helpers import (
    format_number, format_percentage, format_currency,
    display_metric_cards, create_line_chart, get_color_for_value
)

def dashboard_page():
    """仪表板主页面"""

    # 页面标题和欢迎信息
    st.markdown("""
    <div style="text-align: center; padding: 2rem 0 1rem 0;">
        <h1>📊 市场概览</h1>
        <p style="color: #666; font-size: 1.1rem;">实时掌握市场动态，洞察投资机会</p>
    </div>
    """, unsafe_allow_html=True)

    # 初始化股票管理器
    stock_manager = StockManager()

    # 使用容器来组织布局
    with st.container():
        # 显示市场概览
        show_market_overview(stock_manager)

    # 添加适当的间距
    st.markdown("<br>", unsafe_allow_html=True)

    with st.container():
        # 显示热门股票
        show_popular_stocks(stock_manager)

    # 添加适当的间距
    st.markdown("<br>", unsafe_allow_html=True)

    with st.container():
        # 显示用户投资组合摘要
        show_portfolio_summary()

def show_market_overview(stock_manager: StockManager):
    """显示市场概览"""

    # 使用卡片式布局
    st.markdown("""
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                padding: 1.5rem; border-radius: 15px; margin-bottom: 2rem;">
        <h3 style="color: white; margin: 0; text-align: center;">🏢 市场数据概览</h3>
    </div>
    """, unsafe_allow_html=True)

    # 获取所有股票
    stocks = stock_manager.get_all_stocks()

    if not stocks:
        st.warning("⚠️ 暂无股票数据，请检查数据库连接")
        return

    # 计算市场统计数据
    total_stocks = len(stocks)

    # 按行业分组统计
    industry_counts = {}
    for stock in stocks:
        industry = stock.industry or "其他"
        industry_counts[industry] = industry_counts.get(industry, 0) + 1

    # 显示统计指标 - 使用更好的布局
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric(
            label="📊 股票总数",
            value=total_stocks,
            help="系统中的股票总数量"
        )

    with col2:
        st.metric(
            label="🏭 行业数量",
            value=len(industry_counts),
            help="涵盖的行业类别数"
        )

    with col3:
        latest_date = datetime.now().strftime("%m月%d日")
        st.metric(
            label="📅 最新交易日",
            value=latest_date,
            help="最近的交易日期"
        )

    with col4:
        st.metric(
            label="✅ 活跃股票",
            value=total_stocks,
            help="当前活跃的股票数量"
        )

    # 显示行业分布 - 使用更好的布局
    if industry_counts:
        st.markdown("#### 📈 行业分布统计")

        # 创建两列布局
        col1, col2 = st.columns([2, 1])

        with col1:
            # 创建行业分布图表
            industry_df = pd.DataFrame(
                list(industry_counts.items()),
                columns=['行业', '股票数量']
            ).sort_values('股票数量', ascending=False)

            st.bar_chart(industry_df.set_index('行业'), height=300)

        with col2:
            st.markdown("**行业详情**")
            for industry, count in sorted(industry_counts.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total_stocks) * 100
                st.write(f"• **{industry}**: {count}只 ({percentage:.1f}%)")

def show_popular_stocks(stock_manager: StockManager):
    """显示热门股票"""

    # 使用卡片式标题
    st.markdown("""
    <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                padding: 1.5rem; border-radius: 15px; margin-bottom: 1.5rem;">
        <h3 style="color: white; margin: 0; text-align: center;">🔥 热门股票排行</h3>
    </div>
    """, unsafe_allow_html=True)

    # 获取所有股票
    stocks = stock_manager.get_all_stocks()

    if not stocks:
        st.warning("⚠️ 暂无股票数据")
        return
    
    # 创建股票表格数据
    stock_data = []
    
    for stock in stocks[:10]:  # 只显示前10只股票
        # 获取最新价格
        latest_price = stock_manager.get_latest_price(stock.stock_code)
        
        # 计算收益率指标
        returns = stock_manager.calculate_returns(stock.stock_code, 30)
        
        stock_data.append({
            '股票代码': stock.stock_code,
            '股票名称': stock.stock_name,
            '所属行业': stock.industry or "N/A",
            '最新价格': format_currency(latest_price) if latest_price else "N/A",
            '30日收益率': format_percentage(returns.get('total_return', 0)),
            '年化收益率': format_percentage(returns.get('annual_return', 0)),
            '波动率': format_percentage(returns.get('volatility', 0)),
            '夏普比率': f"{returns.get('sharpe_ratio', 0):.2f}",
            '最大回撤': format_percentage(returns.get('max_drawdown', 0))
        })
    
    if stock_data:
        df = pd.DataFrame(stock_data)
        
        # 使用可交互的数据表格
        st.dataframe(
            df,
            use_container_width=True,
            hide_index=True
        )
        
        # 添加股票选择器用于查看详细信息
        st.subheader("📋 股票详细信息")

        selected_stock = st.selectbox(
            "🔍 选择股票查看详细走势图",
            options=[f"{stock['股票代码']} - {stock['股票名称']}" for stock in stock_data],
            index=0
        )
        
        if selected_stock:
            stock_code = selected_stock.split(' - ')[0]
            show_stock_chart(stock_manager, stock_code)

def show_stock_chart(stock_manager: StockManager, stock_code: str):
    """显示股票价格走势图"""
    
    # 获取股票数据
    end_date = date.today()
    start_date = end_date - timedelta(days=90)  # 显示最近3个月数据
    
    df = stock_manager.get_stock_data(stock_code, start_date, end_date)
    
    if not df.empty:
        # 创建价格走势图
        fig = create_line_chart(df, 'close_price', f"{stock_code} 价格走势（最近3个月）")
        st.plotly_chart(fig, use_container_width=True)
        
        # 显示最近5天的数据
        st.subheader("📅 最近交易数据")
        recent_data = df.tail(5)[['open_price', 'high_price', 'low_price', 'close_price', 'volume']]
        recent_data.columns = ['开盘价', '最高价', '最低价', '收盘价', '成交量']
        
        # 格式化显示
        for col in ['开盘价', '最高价', '最低价', '收盘价']:
            recent_data[col] = recent_data[col].apply(lambda x: f"¥{x:.2f}")
        recent_data['成交量'] = recent_data['成交量'].apply(format_number)
        
        st.dataframe(recent_data, use_container_width=True)
    else:
        st.warning(f"暂无 {stock_code} 的历史数据")

def show_portfolio_summary():
    """显示投资组合摘要"""

    # 使用卡片式标题
    st.markdown("""
    <div style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                padding: 1.5rem; border-radius: 15px; margin-bottom: 1.5rem;">
        <h3 style="color: white; margin: 0; text-align: center;">💼 投资组合概览</h3>
    </div>
    """, unsafe_allow_html=True)

    # 创建两列布局
    col1, col2 = st.columns([2, 1])

    with col1:
        st.markdown("""
        <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 10px; border-left: 4px solid #007bff;">
            <h4 style="margin-top: 0; color: #007bff;">💡 投资组合功能</h4>
            <p style="margin-bottom: 0.5rem;">• 创建个性化投资组合</p>
            <p style="margin-bottom: 0.5rem;">• 设置股票权重分配</p>
            <p style="margin-bottom: 0.5rem;">• 分析风险收益指标</p>
            <p style="margin-bottom: 0;">• 跟踪投资表现</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown("<br>", unsafe_allow_html=True)
        if st.button("🚀 立即创建投资组合", use_container_width=True, type="primary"):
            st.session_state.current_page = "💼 投资组合分析"
            st.rerun()

        if st.button("📊 查看示例组合", use_container_width=True):
            st.session_state.show_sample_portfolio = True
            st.rerun()

    # 显示示例投资组合数据（如果用户点击了查看示例）
    if st.session_state.get('show_sample_portfolio', False):
        st.markdown("#### 📊 示例投资组合表现")

        # 使用更美观的指标卡片
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.markdown("""
            <div style="background: white; padding: 1rem; border-radius: 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
                <h4 style="color: #28a745; margin: 0;">¥100,000</h4>
                <p style="margin: 0; color: #666;">总资产</p>
                <small style="color: #28a745;">+5.2%</small>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            st.markdown("""
            <div style="background: white; padding: 1rem; border-radius: 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
                <h4 style="color: #17a2b8; margin: 0;">¥1,250</h4>
                <p style="margin: 0; color: #666;">今日收益</p>
                <small style="color: #17a2b8;">+1.25%</small>
            </div>
            """, unsafe_allow_html=True)

        with col3:
            st.markdown("""
            <div style="background: white; padding: 1rem; border-radius: 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
                <h4 style="color: #ffc107; margin: 0;">15.8%</h4>
                <p style="margin: 0; color: #666;">总收益率</p>
                <small style="color: #ffc107;">+2.1%</small>
            </div>
            """, unsafe_allow_html=True)

        with col4:
            st.markdown("""
            <div style="background: white; padding: 1rem; border-radius: 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
                <h4 style="color: #6f42c1; margin: 0;">5只</h4>
                <p style="margin: 0; color: #666;">持仓股票</p>
                <small style="color: #6f42c1;">均衡配置</small>
            </div>
            """, unsafe_allow_html=True)

        # 添加隐藏按钮
        if st.button("🙈 隐藏示例", key="hide_sample"):
            st.session_state.show_sample_portfolio = False
            st.rerun()
