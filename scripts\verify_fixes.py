"""
修复验证脚本
Fix Verification Script

验证所有语法错误和界面本地化修复是否成功
"""

import os
import sys
import ast
from pathlib import Path

def check_syntax_errors():
    """检查语法错误"""
    print("🔍 检查语法错误...")
    
    project_root = Path(__file__).parent.parent
    python_files = []
    
    # 查找所有Python文件
    for root, dirs, files in os.walk(project_root):
        dirs[:] = [d for d in dirs if d not in ['.git', '__pycache__', 'venv', 'env']]
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    errors = []
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            ast.parse(content)
        except SyntaxError as e:
            rel_path = os.path.relpath(file_path, project_root)
            errors.append(f"{rel_path}: {e.msg} (行 {e.lineno})")
    
    if errors:
        print("❌ 发现语法错误:")
        for error in errors:
            print(f"   {error}")
        return False
    else:
        print("✅ 所有Python文件语法检查通过")
        return True

def check_chinese_interface():
    """检查中文界面元素"""
    print("\n🔍 检查界面中文化...")
    
    chinese_elements = [
        ("app/main.py", "🧭 选择功能模块"),
        ("app/main.py", "🚪 退出登录"),
        ("app/auth/login.py", "🔑 用户登录"),
        ("app/auth/login.py", "📝 新用户注册"),
        ("app/auth/login.py", "🔐 立即登录"),
        ("app/auth/login.py", "🎯 体验演示"),
        ("app/pages/dashboard.py", "💼 创建投资组合"),
        ("app/pages/stock_analysis.py", "🎯 选择要分析的股票"),
        ("app/pages/portfolio_analysis.py", "🎯 创建组合"),
        ("app/pages/portfolio_analysis.py", "📊 组合分析"),
        ("app/pages/portfolio_analysis.py", "📈 历史表现"),
    ]
    
    missing_elements = []
    
    for file_path, element in chinese_elements:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            if element not in content:
                missing_elements.append(f"{file_path}: 缺少 '{element}'")
        except FileNotFoundError:
            missing_elements.append(f"{file_path}: 文件不存在")
    
    if missing_elements:
        print("❌ 中文界面元素缺失:")
        for missing in missing_elements:
            print(f"   {missing}")
        return False
    else:
        print("✅ 中文界面元素检查通过")
        return True

def check_config_files():
    """检查配置文件"""
    print("\n🔍 检查配置文件...")
    
    required_files = [
        ".streamlit/config.toml",
        "scripts/check_syntax.py",
        "scripts/verify_fixes.py"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 配置文件缺失:")
        for missing in missing_files:
            print(f"   {missing}")
        return False
    else:
        print("✅ 配置文件检查通过")
        return True

def check_streamlit_config():
    """检查Streamlit配置"""
    print("\n🔍 检查Streamlit配置...")
    
    config_path = ".streamlit/config.toml"
    
    if not os.path.exists(config_path):
        print("❌ Streamlit配置文件不存在")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_settings = [
            'fileWatcherType = "poll"',
            'address = "localhost"',
            'port = 8501'
        ]
        
        missing_settings = []
        for setting in required_settings:
            if setting not in content:
                missing_settings.append(setting)
        
        if missing_settings:
            print("❌ Streamlit配置缺失:")
            for missing in missing_settings:
                print(f"   {missing}")
            return False
        else:
            print("✅ Streamlit配置检查通过")
            return True
            
    except Exception as e:
        print(f"❌ 读取Streamlit配置失败: {e}")
        return False

def show_startup_instructions():
    """显示启动说明"""
    print("\n🚀 启动说明:")
    print("=" * 50)
    print("1. 激活conda环境:")
    print("   conda activate stock")
    print()
    print("2. 启动应用 (推荐方法):")
    print("   python scripts/start_windows.py")
    print()
    print("3. 或使用直接命令:")
    print("   streamlit run app/main.py --server.port=8501 --server.address=localhost")
    print()
    print("4. 访问应用:")
    print("   http://localhost:8501")
    print()
    print("5. 演示账户:")
    print("   用户名: admin")
    print("   密码: admin123")

def main():
    """主函数"""
    print("🔧 股票分析应用修复验证")
    print("=" * 60)
    
    # 执行所有检查
    checks = [
        check_syntax_errors(),
        check_chinese_interface(),
        check_config_files(),
        check_streamlit_config()
    ]
    
    # 汇总结果
    print("\n📊 验证结果:")
    print("=" * 50)
    
    if all(checks):
        print("🎉 所有修复验证通过！")
        print("✅ 语法错误已修复")
        print("✅ 界面已完全中文化")
        print("✅ 配置文件已创建")
        print("✅ Streamlit配置已优化")
        
        show_startup_instructions()
        return True
    else:
        print("❌ 部分验证失败，请检查上述错误")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
