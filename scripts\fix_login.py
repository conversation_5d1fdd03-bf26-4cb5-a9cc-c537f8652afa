"""
登录问题快速修复脚本
Quick Login Fix Script

快速修复常见的登录问题
"""

import sys
import os
from pathlib import Path
import bcrypt
import mysql.connector
from mysql.connector import Error

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

def fix_admin_password():
    """修复admin用户密码"""
    print("🔧 修复admin用户密码...")
    
    password = "admin123"
    
    try:
        # 生成正确的密码哈希
        salt = bcrypt.gensalt()
        password_hash = bcrypt.hashpw(password.encode('utf-8'), salt)
        hash_str = password_hash.decode('utf-8')
        
        print(f"生成新的密码哈希: {hash_str}")
        
        # 连接数据库
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='123456',
            database='stock_analysis'
        )
        
        cursor = connection.cursor()
        
        # 检查admin用户是否存在
        cursor.execute("SELECT id FROM users WHERE username = 'admin'")
        user_exists = cursor.fetchone()
        
        if user_exists:
            # 更新现有用户的密码
            update_query = "UPDATE users SET password_hash = %s WHERE username = 'admin'"
            cursor.execute(update_query, (hash_str,))
            print("✅ 更新了现有admin用户的密码")
        else:
            # 创建新的admin用户
            insert_query = """
                INSERT INTO users (username, password_hash, email, is_active) 
                VALUES (%s, %s, %s, %s)
            """
            cursor.execute(insert_query, ('admin', hash_str, '<EMAIL>', True))
            print("✅ 创建了新的admin用户")
        
        connection.commit()
        
        # 验证修复
        cursor.execute("SELECT password_hash FROM users WHERE username = 'admin'")
        result = cursor.fetchone()
        
        if result:
            stored_hash = result[0]
            # 验证密码
            is_valid = bcrypt.checkpw(password.encode('utf-8'), stored_hash.encode('utf-8'))
            
            if is_valid:
                print("✅ 密码修复成功，验证通过")
                return True
            else:
                print("❌ 密码修复失败，验证不通过")
                return False
        else:
            print("❌ 无法找到admin用户")
            return False
            
    except Error as e:
        print(f"❌ 数据库操作失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 修复过程出错: {e}")
        return False
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def test_login():
    """测试登录功能"""
    print("\n🧪 测试登录功能...")
    
    try:
        from app.models.user import UserManager
        
        user_manager = UserManager()
        
        # 测试admin用户登录
        user = user_manager.authenticate_user("admin", "admin123")
        
        if user:
            print("✅ admin用户登录测试成功")
            print(f"   用户ID: {user.id}")
            print(f"   用户名: {user.username}")
            print(f"   邮箱: {user.email}")
            print(f"   状态: {'激活' if user.is_active else '未激活'}")
            return True
        else:
            print("❌ admin用户登录测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 登录测试出错: {e}")
        return False

def check_database_connection():
    """检查数据库连接"""
    print("🔍 检查数据库连接...")
    
    try:
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='123456',
            database='stock_analysis'
        )
        
        if connection.is_connected():
            print("✅ 数据库连接成功")
            
            cursor = connection.cursor()
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            print(f"✅ 用户表中有 {user_count} 个用户")
            
            cursor.close()
            connection.close()
            return True
        else:
            print("❌ 数据库连接失败")
            return False
            
    except Error as e:
        print(f"❌ 数据库连接错误: {e}")
        return False

def main():
    """主函数"""
    print("🚀 股票分析应用登录问题快速修复")
    print("=" * 60)
    
    # 1. 检查数据库连接
    if not check_database_connection():
        print("\n❌ 数据库连接失败，请检查MySQL服务和配置")
        return False
    
    # 2. 修复admin密码
    if not fix_admin_password():
        print("\n❌ 密码修复失败")
        return False
    
    # 3. 测试登录功能
    if not test_login():
        print("\n❌ 登录测试失败")
        return False
    
    # 4. 成功提示
    print("\n🎉 登录问题修复完成！")
    print("=" * 50)
    print("✅ 数据库连接正常")
    print("✅ admin用户密码已修复")
    print("✅ 登录功能测试通过")
    print("\n💡 现在可以使用以下账户登录:")
    print("   🌐 访问地址: http://localhost:8501")
    print("   👤 用户名: admin")
    print("   🔑 密码: admin123")
    print("\n🚀 启动应用命令:")
    print("   streamlit run app/main.py --server.port=8501 --server.address=localhost")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 修复完成，可以重新启动应用进行测试")
    else:
        print("\n❌ 修复失败，请检查错误信息")
    
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
