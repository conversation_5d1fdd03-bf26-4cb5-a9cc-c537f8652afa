"""
用户登录认证模块
User Login Authentication Module

处理用户登录、注册和会话管理
"""

import streamlit as st
from app.models.user import UserManager
import time

def login_page():
    """显示登录页面"""
    
    st.markdown("""
    <div style="text-align: center; padding: 2rem;">
        <h1>📈 股票分析系统</h1>
        <h3>Stock Analysis System</h3>
        <p>欢迎使用专业的股票数据分析平台</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 创建登录表单
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.markdown("### 用户登录")
        
        # 创建选项卡
        tab1, tab2 = st.tabs(["🔑 用户登录", "📝 新用户注册"])
        
        with tab1:
            login_form()
        
        with tab2:
            register_form()

def login_form():
    """登录表单"""
    user_manager = UserManager()
    
    with st.form("login_form"):
        st.markdown("#### 请输入您的登录信息")
        
        username = st.text_input("用户名", placeholder="请输入用户名")
        password = st.text_input("密码", type="password", placeholder="请输入密码")
        
        col1, col2 = st.columns(2)
        with col1:
            login_button = st.form_submit_button("🔐 立即登录", use_container_width=True)
        with col2:
            demo_button = st.form_submit_button("🎯 体验演示", use_container_width=True)
        
        if login_button:
            if username and password:
                # 验证用户登录
                user = user_manager.authenticate_user(username, password)
                if user:
                    st.session_state.logged_in = True
                    st.session_state.username = user.username
                    st.session_state.user_id = user.id
                    st.success("✅ 登录成功！正在跳转到主页...")
                    time.sleep(1)
                    st.rerun()
                else:
                    st.error("❌ 用户名或密码错误，请检查后重试")
            else:
                st.warning("⚠️ 请输入完整的用户名和密码")
        
        if demo_button:
            # 使用演示账户登录
            demo_user = user_manager.authenticate_user("admin", "admin123")
            if demo_user:
                st.session_state.logged_in = True
                st.session_state.username = demo_user.username
                st.session_state.user_id = demo_user.id
                st.success("🎉 演示账户登录成功！正在跳转到主页...")
                time.sleep(1)
                st.rerun()
            else:
                st.error("❌ 演示账户暂时不可用，请联系系统管理员")

def register_form():
    """注册表单"""
    user_manager = UserManager()
    
    with st.form("register_form"):
        st.markdown("#### 创建新账户")
        
        new_username = st.text_input("用户名", placeholder="请输入用户名（3-20个字符）")
        new_email = st.text_input("邮箱", placeholder="请输入邮箱地址（可选）")
        new_password = st.text_input("密码", type="password", placeholder="请输入密码（至少6个字符）")
        confirm_password = st.text_input("确认密码", type="password", placeholder="请再次输入密码")
        
        register_button = st.form_submit_button("📝 立即注册", use_container_width=True)
        
        if register_button:
            # 验证输入
            if not new_username or len(new_username) < 3:
                st.error("❌ 用户名至少需要3个字符")
                return

            if not new_password or len(new_password) < 6:
                st.error("❌ 密码至少需要6个字符")
                return

            if new_password != confirm_password:
                st.error("❌ 两次输入的密码不一致，请重新输入")
                return
            
            # 尝试创建用户
            if user_manager.create_user(new_username, new_password, new_email):
                st.success("🎉 注册成功！请使用新账户登录")
                time.sleep(2)
                st.rerun()
            else:
                st.error("❌ 注册失败，用户名可能已存在")

def logout():
    """用户登出"""
    st.session_state.logged_in = False
    st.session_state.username = None
    st.session_state.user_id = None
    st.rerun()

def check_authentication():
    """检查用户认证状态"""
    if 'logged_in' not in st.session_state:
        st.session_state.logged_in = False
    
    return st.session_state.logged_in

def get_current_user():
    """获取当前登录用户信息"""
    if check_authentication():
        return {
            'username': st.session_state.get('username'),
            'user_id': st.session_state.get('user_id')
        }
    return None
