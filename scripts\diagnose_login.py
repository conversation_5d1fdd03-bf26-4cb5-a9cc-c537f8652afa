"""
登录问题诊断脚本
Login Issue Diagnosis Script

诊断用户登录问题，检查密码哈希和认证逻辑
"""

import sys
import os
from pathlib import Path
import bcrypt
import mysql.connector
from mysql.connector import Error

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

def test_bcrypt():
    """测试bcrypt密码加密和验证"""
    print("🔐 测试bcrypt密码加密...")
    
    test_password = "admin123"
    
    try:
        # 生成新的密码哈希
        new_hash = bcrypt.hashpw(test_password.encode('utf-8'), bcrypt.gensalt())
        print(f"✅ 新生成的密码哈希: {new_hash.decode('utf-8')}")
        
        # 验证密码
        is_valid = bcrypt.checkpw(test_password.encode('utf-8'), new_hash)
        print(f"✅ 密码验证结果: {is_valid}")
        
        return new_hash.decode('utf-8')
        
    except Exception as e:
        print(f"❌ bcrypt测试失败: {e}")
        return None

def check_database_users():
    """检查数据库中的用户数据"""
    print("\n📊 检查数据库用户数据...")
    
    try:
        # 连接数据库
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='123456',
            database='stock_analysis'
        )
        
        cursor = connection.cursor(dictionary=True)
        
        # 查询用户数据
        cursor.execute("SELECT * FROM users WHERE username = 'admin'")
        user_data = cursor.fetchone()
        
        if user_data:
            print("✅ 找到admin用户:")
            print(f"   ID: {user_data['id']}")
            print(f"   用户名: {user_data['username']}")
            print(f"   邮箱: {user_data['email']}")
            print(f"   密码哈希: {user_data['password_hash']}")
            print(f"   是否激活: {user_data['is_active']}")
            print(f"   创建时间: {user_data['created_at']}")
            
            return user_data
        else:
            print("❌ 未找到admin用户")
            return None
            
    except Error as e:
        print(f"❌ 数据库查询失败: {e}")
        return None
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()

def test_password_verification(stored_hash, test_password="admin123"):
    """测试密码验证"""
    print(f"\n🔍 测试密码验证...")
    print(f"测试密码: {test_password}")
    print(f"存储的哈希: {stored_hash}")
    
    try:
        # 测试密码验证
        is_valid = bcrypt.checkpw(test_password.encode('utf-8'), stored_hash.encode('utf-8'))
        print(f"验证结果: {is_valid}")
        
        if is_valid:
            print("✅ 密码验证成功")
        else:
            print("❌ 密码验证失败")
            
        return is_valid
        
    except Exception as e:
        print(f"❌ 密码验证出错: {e}")
        return False

def generate_correct_hash():
    """生成正确的密码哈希"""
    print("\n🔧 生成正确的密码哈希...")
    
    password = "admin123"
    
    try:
        # 生成密码哈希
        salt = bcrypt.gensalt()
        password_hash = bcrypt.hashpw(password.encode('utf-8'), salt)
        hash_str = password_hash.decode('utf-8')
        
        print(f"密码: {password}")
        print(f"新哈希: {hash_str}")
        
        # 验证新哈希
        is_valid = bcrypt.checkpw(password.encode('utf-8'), password_hash)
        print(f"验证结果: {is_valid}")
        
        return hash_str
        
    except Exception as e:
        print(f"❌ 生成哈希失败: {e}")
        return None

def update_admin_password(new_hash):
    """更新admin用户的密码哈希"""
    print(f"\n🔄 更新admin用户密码...")
    
    try:
        # 连接数据库
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='123456',
            database='stock_analysis'
        )
        
        cursor = connection.cursor()
        
        # 更新密码哈希
        update_query = "UPDATE users SET password_hash = %s WHERE username = 'admin'"
        cursor.execute(update_query, (new_hash,))
        connection.commit()
        
        print("✅ admin用户密码已更新")
        
        # 验证更新
        cursor.execute("SELECT password_hash FROM users WHERE username = 'admin'")
        result = cursor.fetchone()
        
        if result and result[0] == new_hash:
            print("✅ 密码更新验证成功")
            return True
        else:
            print("❌ 密码更新验证失败")
            return False
            
    except Error as e:
        print(f"❌ 更新密码失败: {e}")
        return False
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()

def test_user_authentication():
    """测试用户认证功能"""
    print("\n🧪 测试用户认证功能...")
    
    try:
        from app.models.user import UserManager
        
        user_manager = UserManager()
        
        # 测试认证
        user = user_manager.authenticate_user("admin", "admin123")
        
        if user:
            print("✅ 用户认证成功")
            print(f"   用户ID: {user.id}")
            print(f"   用户名: {user.username}")
            print(f"   邮箱: {user.email}")
            return True
        else:
            print("❌ 用户认证失败")
            return False
            
    except Exception as e:
        print(f"❌ 认证测试出错: {e}")
        return False

def main():
    """主函数"""
    print("🔍 股票分析应用登录问题诊断")
    print("=" * 60)
    
    # 1. 测试bcrypt功能
    new_hash = test_bcrypt()
    
    # 2. 检查数据库用户数据
    user_data = check_database_users()
    
    if user_data:
        # 3. 测试当前密码验证
        stored_hash = user_data['password_hash']
        is_current_valid = test_password_verification(stored_hash)
        
        if not is_current_valid:
            print("\n🔧 当前密码哈希无效，需要更新...")
            
            # 4. 生成正确的密码哈希
            correct_hash = generate_correct_hash()
            
            if correct_hash:
                # 5. 更新数据库中的密码
                if update_admin_password(correct_hash):
                    print("\n✅ 密码已修复")
                else:
                    print("\n❌ 密码修复失败")
                    return False
            else:
                print("\n❌ 无法生成正确的密码哈希")
                return False
        else:
            print("\n✅ 当前密码哈希有效")
    else:
        print("\n❌ 无法获取用户数据")
        return False
    
    # 6. 测试用户认证功能
    auth_success = test_user_authentication()
    
    # 7. 总结
    print("\n📊 诊断结果:")
    print("=" * 50)
    
    if auth_success:
        print("🎉 登录问题已修复！")
        print("✅ 数据库连接正常")
        print("✅ 用户数据存在")
        print("✅ 密码哈希正确")
        print("✅ 用户认证成功")
        print("\n💡 现在可以使用以下账户登录:")
        print("   用户名: admin")
        print("   密码: admin123")
        return True
    else:
        print("❌ 登录问题仍然存在")
        print("请检查上述错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
