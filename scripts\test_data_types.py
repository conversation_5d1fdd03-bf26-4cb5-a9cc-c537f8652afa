"""
数据类型修复测试脚本
Data Type Fix Test Script

测试修复后的数据类型转换和收益率计算功能
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

def test_decimal_conversion():
    """测试Decimal类型转换"""
    print("🔍 测试Decimal类型转换...")
    
    try:
        # 模拟从数据库获取的Decimal数据
        test_data = {
            'open_price': [Decimal('10.50'), Decimal('10.60'), Decimal('10.70')],
            'close_price': [Decimal('10.55'), Decimal('10.65'), Decimal('10.75')],
            'volume': [1000000, 1100000, 1200000]
        }
        
        df = pd.DataFrame(test_data)
        print(f"原始数据类型: {df.dtypes.to_dict()}")
        
        # 测试数据类型转换
        numeric_columns = ['open_price', 'close_price', 'volume']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        print(f"转换后数据类型: {df.dtypes.to_dict()}")
        
        # 测试数学运算
        try:
            result = float(df['close_price'].iloc[-1]) ** 2
            print(f"✅ 幂运算测试成功: {result}")
            
            result2 = np.sqrt(float(df['close_price'].iloc[0]))
            print(f"✅ 开方运算测试成功: {result2}")
            
            return True
        except Exception as e:
            print(f"❌ 数学运算测试失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Decimal转换测试失败: {e}")
        return False

def test_stock_data_retrieval():
    """测试股票数据获取"""
    print("\n🔍 测试股票数据获取...")
    
    try:
        from app.models.stock import StockManager
        
        stock_manager = StockManager()
        
        # 获取所有股票
        stocks = stock_manager.get_all_stocks()
        if not stocks:
            print("❌ 没有找到股票数据")
            return False
        
        print(f"✅ 找到 {len(stocks)} 只股票")
        
        # 测试获取第一只股票的数据
        first_stock = stocks[0]
        print(f"测试股票: {first_stock.stock_code} - {first_stock.stock_name}")
        
        # 获取股票历史数据
        stock_data = stock_manager.get_stock_data(first_stock.stock_code)
        
        if stock_data.empty:
            print("❌ 股票历史数据为空")
            return False
        
        print(f"✅ 获取到 {len(stock_data)} 条历史数据")
        print(f"数据类型: {stock_data.dtypes.to_dict()}")
        
        # 检查数据类型
        price_columns = ['open_price', 'high_price', 'low_price', 'close_price']
        for col in price_columns:
            if col in stock_data.columns:
                dtype = stock_data[col].dtype
                print(f"  {col}: {dtype}")
                if not pd.api.types.is_numeric_dtype(dtype):
                    print(f"⚠️ {col} 不是数值类型")
        
        return True
        
    except Exception as e:
        print(f"❌ 股票数据获取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_returns_calculation():
    """测试收益率计算"""
    print("\n🔍 测试收益率计算...")
    
    try:
        from app.models.stock import StockManager
        
        stock_manager = StockManager()
        
        # 获取第一只股票
        stocks = stock_manager.get_all_stocks()
        if not stocks:
            print("❌ 没有找到股票数据")
            return False
        
        first_stock = stocks[0]
        print(f"测试股票: {first_stock.stock_code} - {first_stock.stock_name}")
        
        # 计算收益率指标
        returns = stock_manager.calculate_returns(first_stock.stock_code, 30)
        
        if not returns:
            print("❌ 收益率计算返回空结果")
            return False
        
        print("✅ 收益率计算成功:")
        for key, value in returns.items():
            print(f"  {key}: {value}")
            
            # 检查返回值类型
            if not isinstance(value, (int, float)):
                print(f"⚠️ {key} 的值不是数值类型: {type(value)}")
        
        # 测试不同周期
        test_periods = [7, 30, 90, 180]
        for period in test_periods:
            period_returns = stock_manager.calculate_returns(first_stock.stock_code, period)
            if period_returns:
                print(f"✅ {period}天周期计算成功")
            else:
                print(f"⚠️ {period}天周期计算失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 收益率计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chart_creation():
    """测试图表创建"""
    print("\n🔍 测试图表创建...")
    
    try:
        from app.models.stock import StockManager
        from app.utils.helpers import create_candlestick_chart, create_line_chart
        
        stock_manager = StockManager()
        
        # 获取股票数据
        stocks = stock_manager.get_all_stocks()
        if not stocks:
            print("❌ 没有找到股票数据")
            return False
        
        first_stock = stocks[0]
        stock_data = stock_manager.get_stock_data(first_stock.stock_code)
        
        if stock_data.empty:
            print("❌ 股票数据为空")
            return False
        
        # 测试K线图创建
        try:
            candlestick_fig = create_candlestick_chart(stock_data, f"{first_stock.stock_name} K线图")
            print("✅ K线图创建成功")
        except Exception as e:
            print(f"❌ K线图创建失败: {e}")
            return False
        
        # 测试折线图创建
        try:
            line_fig = create_line_chart(stock_data, 'close_price', f"{first_stock.stock_name} 价格走势")
            print("✅ 折线图创建成功")
        except Exception as e:
            print(f"❌ 折线图创建失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 图表创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_data_types():
    """测试数据库数据类型"""
    print("\n🔍 测试数据库数据类型...")
    
    try:
        import mysql.connector
        from mysql.connector import Error
        
        # 连接数据库
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='123456',
            database='stock_analysis'
        )
        
        cursor = connection.cursor(dictionary=True)
        
        # 查询股票数据的数据类型
        cursor.execute("DESCRIBE stock_data")
        columns_info = cursor.fetchall()
        
        print("数据库表结构:")
        for col_info in columns_info:
            print(f"  {col_info['Field']}: {col_info['Type']}")
        
        # 查询一条示例数据
        cursor.execute("SELECT * FROM stock_data LIMIT 1")
        sample_data = cursor.fetchone()
        
        if sample_data:
            print("\n示例数据类型:")
            for key, value in sample_data.items():
                print(f"  {key}: {type(value)} = {value}")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Error as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 数据类型修复测试")
    print("=" * 60)
    
    # 执行所有测试
    tests = [
        ("Decimal类型转换", test_decimal_conversion),
        ("股票数据获取", test_stock_data_retrieval),
        ("收益率计算", test_returns_calculation),
        ("图表创建", test_chart_creation),
        ("数据库数据类型", test_database_data_types)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试出错: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n📊 测试结果汇总:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 所有数据类型修复测试通过！")
        print("✅ Decimal类型转换正常")
        print("✅ 收益率计算功能正常")
        print("✅ 图表创建功能正常")
        print("✅ 数据库数据类型处理正常")
        return True
    else:
        print(f"\n⚠️ {total - passed} 项测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
