"""
用户数据模型
User Data Model

定义用户相关的数据结构和操作方法
"""

from dataclasses import dataclass
from typing import Optional, List
from datetime import datetime
import bcrypt
from app.database.connection import get_db_connection

@dataclass
class User:
    """用户数据模型类"""
    id: Optional[int] = None
    username: str = ""
    password_hash: str = ""
    email: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_active: bool = True

class UserManager:
    """用户管理器类"""
    
    def __init__(self):
        self.db = get_db_connection()
    
    def create_user(self, username: str, password: str, email: Optional[str] = None) -> bool:
        """创建新用户"""
        try:
            # 检查用户名是否已存在
            if self.get_user_by_username(username):
                return False
            
            # 加密密码
            password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            # 插入用户数据
            query = """
                INSERT INTO users (username, password_hash, email) 
                VALUES (%s, %s, %s)
            """
            return self.db.execute_update(query, (username, password_hash, email))
        except Exception as e:
            print(f"创建用户失败: {e}")
            return False
    
    def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """验证用户登录"""
        try:
            user = self.get_user_by_username(username)
            if user and bcrypt.checkpw(password.encode('utf-8'), user.password_hash.encode('utf-8')):
                return user
            return None
        except Exception as e:
            print(f"用户认证失败: {e}")
            return None
    
    def get_user_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户信息"""
        try:
            query = "SELECT * FROM users WHERE username = %s AND is_active = TRUE"
            result = self.db.execute_query(query, (username,))
            
            if result and len(result) > 0:
                user_data = result[0]
                return User(
                    id=user_data['id'],
                    username=user_data['username'],
                    password_hash=user_data['password_hash'],
                    email=user_data['email'],
                    created_at=user_data['created_at'],
                    updated_at=user_data['updated_at'],
                    is_active=user_data['is_active']
                )
            return None
        except Exception as e:
            print(f"获取用户信息失败: {e}")
            return None
    
    def get_user_by_id(self, user_id: int) -> Optional[User]:
        """根据用户ID获取用户信息"""
        try:
            query = "SELECT * FROM users WHERE id = %s AND is_active = TRUE"
            result = self.db.execute_query(query, (user_id,))
            
            if result and len(result) > 0:
                user_data = result[0]
                return User(
                    id=user_data['id'],
                    username=user_data['username'],
                    password_hash=user_data['password_hash'],
                    email=user_data['email'],
                    created_at=user_data['created_at'],
                    updated_at=user_data['updated_at'],
                    is_active=user_data['is_active']
                )
            return None
        except Exception as e:
            print(f"获取用户信息失败: {e}")
            return None
    
    def update_user(self, user_id: int, email: Optional[str] = None) -> bool:
        """更新用户信息"""
        try:
            query = "UPDATE users SET email = %s, updated_at = CURRENT_TIMESTAMP WHERE id = %s"
            return self.db.execute_update(query, (email, user_id))
        except Exception as e:
            print(f"更新用户信息失败: {e}")
            return False
    
    def deactivate_user(self, user_id: int) -> bool:
        """停用用户账户"""
        try:
            query = "UPDATE users SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP WHERE id = %s"
            return self.db.execute_update(query, (user_id,))
        except Exception as e:
            print(f"停用用户失败: {e}")
            return False
