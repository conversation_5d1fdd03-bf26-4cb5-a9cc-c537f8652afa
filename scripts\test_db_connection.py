"""
数据库连接测试脚本
Database Connection Test Script

用于测试本地MySQL数据库连接
"""

import mysql.connector
from mysql.connector import Error
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

def test_mysql_connection():
    """测试MySQL连接"""
    print("🔍 测试MySQL数据库连接...")
    print("=" * 50)
    
    # 数据库配置
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'database': 'stock_analysis'
    }
    
    try:
        # 测试连接到MySQL服务器（不指定数据库）
        print("📡 测试MySQL服务器连接...")
        server_connection = mysql.connector.connect(
            host=config['host'],
            port=config['port'],
            user=config['user'],
            password=config['password']
        )
        
        if server_connection.is_connected():
            print("✅ MySQL服务器连接成功")
            
            # 检查数据库是否存在
            cursor = server_connection.cursor()
            cursor.execute("SHOW DATABASES LIKE 'stock_analysis'")
            result = cursor.fetchone()
            
            if result:
                print("✅ 数据库 'stock_analysis' 存在")
                
                # 测试连接到具体数据库
                print("📊 测试数据库连接...")
                db_connection = mysql.connector.connect(**config)
                
                if db_connection.is_connected():
                    print("✅ 数据库连接成功")
                    
                    # 检查表是否存在
                    db_cursor = db_connection.cursor()
                    db_cursor.execute("SHOW TABLES")
                    tables = db_cursor.fetchall()
                    
                    if tables:
                        print(f"📋 数据库中的表 ({len(tables)}个):")
                        for table in tables:
                            print(f"  - {table[0]}")
                        
                        # 检查用户表数据
                        try:
                            db_cursor.execute("SELECT COUNT(*) FROM users")
                            user_count = db_cursor.fetchone()[0]
                            print(f"👥 用户数量: {user_count}")
                        except Error as e:
                            print(f"⚠️ 查询用户表失败: {e}")
                        
                        # 检查股票表数据
                        try:
                            db_cursor.execute("SELECT COUNT(*) FROM stocks")
                            stock_count = db_cursor.fetchone()[0]
                            print(f"📈 股票数量: {stock_count}")
                        except Error as e:
                            print(f"⚠️ 查询股票表失败: {e}")
                    else:
                        print("⚠️ 数据库中没有表，需要运行初始化脚本")
                    
                    db_cursor.close()
                    db_connection.close()
                else:
                    print("❌ 数据库连接失败")
                    return False
            else:
                print("❌ 数据库 'stock_analysis' 不存在")
                print("💡 请先运行: python scripts/init_db.py")
                return False
            
            cursor.close()
            server_connection.close()
        else:
            print("❌ MySQL服务器连接失败")
            return False
            
    except Error as e:
        print(f"❌ 数据库连接错误: {e}")
        print("\n🔧 可能的解决方案:")
        print("1. 检查MySQL服务是否已启动")
        print("2. 确认用户名和密码是否正确 (root/123456)")
        print("3. 确认MySQL端口是否为3306")
        print("4. 检查防火墙设置")
        return False
    
    print("\n✅ 数据库连接测试完成！")
    return True

def test_application_connection():
    """测试应用程序数据库连接"""
    print("\n🔍 测试应用程序数据库连接...")
    print("=" * 50)
    
    try:
        from app.database.connection import DatabaseManager
        
        db_manager = DatabaseManager()
        if db_manager.connect():
            print("✅ 应用程序数据库连接成功")
            
            # 测试查询
            result = db_manager.execute_query("SELECT COUNT(*) as count FROM users")
            if result:
                print(f"👥 用户表查询成功，用户数量: {result[0]['count']}")
            
            db_manager.disconnect()
            return True
        else:
            print("❌ 应用程序数据库连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 应用程序连接测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 股票分析系统数据库连接测试")
    print("=" * 60)
    
    # 测试MySQL连接
    mysql_ok = test_mysql_connection()
    
    if mysql_ok:
        # 测试应用程序连接
        app_ok = test_application_connection()
        
        if app_ok:
            print("\n🎉 所有数据库连接测试通过！")
            print("💡 您现在可以启动应用程序了")
        else:
            print("\n⚠️ 应用程序连接测试失败")
    else:
        print("\n❌ MySQL连接测试失败，请检查数据库配置")

if __name__ == "__main__":
    main()
