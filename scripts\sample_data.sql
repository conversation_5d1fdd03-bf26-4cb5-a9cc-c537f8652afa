-- 股票分析系统示例数据
-- Sample Data for Stock Analysis System

USE stock_analysis;

-- 插入示例用户数据
INSERT INTO users (username, password_hash, email) VALUES
('admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', '<EMAIL>'),
('user1', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', '<EMAIL>'),
('user2', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', '<EMAIL>');

-- 插入示例股票基础信息
INSERT INTO stocks (stock_code, stock_name, market, industry, sector) VALUES
('000001', '平安银行', 'SZ', '银行', '金融'),
('000002', '万科A', 'SZ', '房地产开发', '房地产'),
('600000', '浦发银行', 'SH', '银行', '金融'),
('600036', '招商银行', 'SH', '银行', '金融'),
('000858', '五粮液', 'SZ', '白酒', '食品饮料');

-- 插入示例股票历史数据（最近30天的模拟数据）
-- 平安银行 (000001) 示例数据
INSERT INTO stock_data (stock_code, trade_date, open_price, high_price, low_price, close_price, volume, amount) VALUES
('000001', '2024-01-02', 12.50, 12.80, 12.30, 12.75, 150000000, 1912500000),
('000001', '2024-01-03', 12.75, 13.00, 12.60, 12.90, 180000000, 2322000000),
('000001', '2024-01-04', 12.90, 13.20, 12.85, 13.10, 200000000, 2620000000),
('000001', '2024-01-05', 13.10, 13.30, 12.95, 13.25, 175000000, 2293750000),
('000001', '2024-01-08', 13.25, 13.45, 13.10, 13.35, 165000000, 2192750000);

-- 万科A (000002) 示例数据
INSERT INTO stock_data (stock_code, trade_date, open_price, high_price, low_price, close_price, volume, amount) VALUES
('000002', '2024-01-02', 8.50, 8.75, 8.40, 8.65, 120000000, 1038000000),
('000002', '2024-01-03', 8.65, 8.80, 8.55, 8.70, 135000000, 1174500000),
('000002', '2024-01-04', 8.70, 8.90, 8.65, 8.85, 145000000, 1283250000),
('000002', '2024-01-05', 8.85, 9.00, 8.75, 8.95, 155000000, 1387250000),
('000002', '2024-01-08', 8.95, 9.10, 8.85, 9.05, 160000000, 1448000000);

-- 浦发银行 (600000) 示例数据
INSERT INTO stock_data (stock_code, trade_date, open_price, high_price, low_price, close_price, volume, amount) VALUES
('600000', '2024-01-02', 7.20, 7.35, 7.15, 7.30, 100000000, 725000000),
('600000', '2024-01-03', 7.30, 7.45, 7.25, 7.40, 110000000, 814000000),
('600000', '2024-01-04', 7.40, 7.55, 7.35, 7.50, 120000000, 900000000),
('600000', '2024-01-05', 7.50, 7.65, 7.45, 7.60, 115000000, 874000000),
('600000', '2024-01-08', 7.60, 7.75, 7.55, 7.70, 125000000, 962500000);

-- 招商银行 (600036) 示例数据
INSERT INTO stock_data (stock_code, trade_date, open_price, high_price, low_price, close_price, volume, amount) VALUES
('600036', '2024-01-02', 35.50, 36.20, 35.30, 36.00, 80000000, 2880000000),
('600036', '2024-01-03', 36.00, 36.50, 35.80, 36.25, 85000000, 3081250000),
('600036', '2024-01-04', 36.25, 36.80, 36.10, 36.55, 90000000, 3289500000),
('600036', '2024-01-05', 36.55, 37.00, 36.40, 36.80, 95000000, 3496000000),
('600036', '2024-01-08', 36.80, 37.20, 36.65, 37.05, 88000000, 3260400000);

-- 五粮液 (000858) 示例数据
INSERT INTO stock_data (stock_code, trade_date, open_price, high_price, low_price, close_price, volume, amount) VALUES
('000858', '2024-01-02', 155.00, 158.50, 154.20, 157.80, 25000000, 3945000000),
('000858', '2024-01-03', 157.80, 160.20, 156.50, 159.50, 28000000, 4466000000),
('000858', '2024-01-04', 159.50, 162.00, 158.80, 161.20, 30000000, 4836000000),
('000858', '2024-01-05', 161.20, 163.50, 160.50, 162.80, 32000000, 5209600000),
('000858', '2024-01-08', 162.80, 165.00, 161.90, 164.50, 29000000, 4770500000);

-- 创建示例投资组合
INSERT INTO user_portfolios (user_id, portfolio_name, description) VALUES
(2, '稳健型组合', '以银行股为主的稳健投资组合'),
(2, '成长型组合', '包含消费和科技股的成长型组合');

-- 插入投资组合持仓数据
INSERT INTO portfolio_holdings (portfolio_id, stock_code, weight) VALUES
(1, '000001', 0.2000), -- 平安银行 20%
(1, '600000', 0.2000), -- 浦发银行 20%
(1, '600036', 0.2000), -- 招商银行 20%
(1, '000002', 0.2000), -- 万科A 20%
(1, '000858', 0.2000); -- 五粮液 20%
